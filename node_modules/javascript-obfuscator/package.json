{"name": "javascript-obfuscator", "version": "4.1.1", "description": "JavaScript obfuscator", "keywords": ["obfuscator", "obfuscation", "uglify", "crush", "code protection", "javascript obfuscator", "js obfuscator"], "engines": {"node": ">=12.22.0"}, "main": "dist/index.js", "browser": "dist/index.browser.js", "bin": {"javascript-obfuscator": "./bin/javascript-obfuscator"}, "types": "typings/index.d.ts", "dependencies": {"@javascript-obfuscator/escodegen": "2.3.0", "@javascript-obfuscator/estraverse": "5.4.0", "acorn": "8.8.2", "assert": "2.0.0", "chalk": "4.1.2", "chance": "1.1.9", "class-validator": "0.14.1", "commander": "10.0.0", "eslint-scope": "7.1.1", "eslint-visitor-keys": "3.3.0", "fast-deep-equal": "3.1.3", "inversify": "6.0.1", "js-string-escape": "1.0.1", "md5": "2.3.0", "mkdirp": "2.1.3", "multimatch": "5.0.0", "opencollective-postinstall": "2.0.3", "process": "0.11.10", "reflect-metadata": "0.1.13", "source-map-support": "0.5.21", "string-template": "1.0.0", "stringz": "2.1.0", "tslib": "2.5.0"}, "devDependencies": {"@istanbuljs/nyc-config-typescript": "1.0.2", "@types/chai": "4.3.4", "@types/chance": "1.1.3", "@types/escodegen": "0.0.7", "@types/eslint-scope": "3.7.4", "@types/eslint-visitor-keys": "1.0.0", "@types/estraverse": "5.1.2", "@types/estree": "0.0.51", "@types/js-beautify": "1.13.3", "@types/js-string-escape": "1.0.1", "@types/md5": "2.3.2", "@types/mkdirp": "1.0.2", "@types/mocha": "10.0.1", "@types/multimatch": "4.0.0", "@types/node": "18.13.0", "@types/rimraf": "3.0.2", "@types/sinon": "10.0.13", "@types/string-template": "1.0.2", "@types/webpack-env": "1.18.0", "@typescript-eslint/eslint-plugin": "5.51.0", "@typescript-eslint/parser": "5.51.0", "chai": "4.3.7", "chai-exclude": "2.1.0", "cross-env": "7.0.3", "eslint": "8.34.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsdoc": "48.2.12", "eslint-plugin-no-null": "1.0.2", "eslint-plugin-prefer-arrow": "1.2.3", "eslint-plugin-unicorn": "45.0.2", "eslint-webpack-plugin": "4.0.0", "fork-ts-checker-notifier-webpack-plugin": "6.0.0", "fork-ts-checker-webpack-plugin": "7.3.0", "husky": "8.0.3", "js-beautify": "1.14.7", "mocha": "10.4.0", "nyc": "15.1.0", "pjson": "1.0.9", "rimraf": "4.1.2", "sinon": "15.0.1", "source-map-resolve": "0.6.0", "terser": "5.16.3", "threads": "1.7.0", "ts-loader": "9.4.2", "ts-node": "10.9.1", "typescript": "4.9.5", "webpack": "5.75.0", "webpack-cli": "5.0.1", "webpack-node-externals": "3.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/javascript-obfuscator/javascript-obfuscator.git"}, "homepage": "https://obfuscator.io/", "scripts": {"start": "yarn run watch", "webpack:prod": "webpack --config ./webpack/webpack.node.config.js --config ./webpack/webpack.browser.config.js --mode production", "build": "yarn run webpack:prod && yarn run eslint && yarn test", "build:typings": "rm -rf ./typings && tsc --project src/tsconfig.typings.json", "watch": "webpack --config ./webpack/webpack.node.config.js --mode development --watch", "test:dev": "ts-node --type-check test/dev/dev.ts", "test:devCompilePerformance": "ts-node test/dev/dev-compile-performance.ts", "test:devRuntimePerformance": "ts-node test/dev/dev-runtime-performance.ts", "test:full": "yarn run test:dev && yarn run test:mocha-coverage && yarn run test:mocha-memory-performance", "test:mocha": "mocha --require ts-node/register --require source-map-support/register test/index.spec.ts --exit", "test:mocha-coverage": "cross-env NODE_OPTIONS=--max-old-space-size=4096 nyc --reporter text-summary --no-clean yarn run test:mocha", "test:mocha-coverage:report": "nyc report --reporter=lcov", "test:mocha-memory-performance": "cross-env NODE_OPTIONS=--max-old-space-size=280 mocha --require ts-node/register test/performance-tests/JavaScriptObfuscatorMemory.spec.ts", "test": "yarn run test:full", "eslint": "eslint src/**/*.ts", "git:addFiles": "git add .", "postinstall": "opencollective-postinstall", "precommit": "npm run build", "prepublishOnly": "npm run build && npm run build:typings", "prepare": "husky install"}, "author": {"name": "<PERSON><PERSON><PERSON>"}, "contributors": ["<PERSON><PERSON><PERSON> (https://github.com/sanex3339)", "<PERSON> (https://github.com/zamotkin)"], "license": "BSD-2-<PERSON><PERSON>", "funding": {"type": "opencollective", "url": "https://opencollective.com/javascript-obfuscator"}, "collective": {"url": "https://opencollective.com/javascript-obfuscator"}}