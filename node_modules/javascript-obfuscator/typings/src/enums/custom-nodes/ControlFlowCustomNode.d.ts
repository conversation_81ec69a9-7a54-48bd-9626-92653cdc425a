export declare enum ControlFlowCustomNode {
    BinaryExpressionFunctionNode = "BinaryExpressionFunctionNode",
    BlockStatementControlFlowFlatteningNode = "BlockStatementControlFlowFlatteningNode",
    CallExpressionControlFlowStorageCallNode = "CallExpressionControlFlowStorageCallNode",
    CallExpressionFunctionNode = "CallExpressionFunctionNode",
    ControlFlowStorageNode = "ControlFlowStorageNode",
    ExpressionWithOperatorControlFlowStorageCallNode = "ExpressionWithOperatorControlFlowStorageCallNode",
    LiteralNode = "LiteralNode",
    LogicalExpressionFunctionNode = "LogicalExpressionFunctionNode",
    StringLiteralControlFlowStorageCallNode = "StringLiteralControlFlowStorageCallNode"
}
