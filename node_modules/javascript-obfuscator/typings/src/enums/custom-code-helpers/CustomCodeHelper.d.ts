export declare enum CustomCodeHelper {
    CallsControllerFunction = "CallsControllerFunction",
    ConsoleOutputDisable = "ConsoleOutputDisable",
    DebugProtectionFunctionCall = "DebugProtectionFunctionCall",
    DebugProtectionFunctionInterval = "DebugProtectionFunctionInterval",
    DebugProtectionFunction = "DebugProtectionFunction",
    DomainLock = "DomainLock",
    SelfDefending = "SelfDefending",
    StringArray = "StringArray",
    StringArrayCallsWrapper = "StringArrayCallsWrapper",
    StringArrayCallsWrapperBase64 = "StringArrayCallsWrapperBase64",
    StringArrayCallsWrapperRc4 = "StringArrayCallsWrapperRc4",
    StringArrayRotateFunction = "StringArrayRotateFunction"
}
