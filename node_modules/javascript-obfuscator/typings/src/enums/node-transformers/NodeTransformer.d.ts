export declare enum NodeTransformer {
    BooleanLiteralTransformer = "BooleanLiteralTransformer",
    BlockStatementControlFlowTransformer = "BlockStatementControlFlowTransformer",
    BlockStatementSimplifyTransformer = "BlockStatementSimplifyTransformer",
    ClassFieldTransformer = "ClassFieldTransformer",
    CommentsTransformer = "CommentsTransformer",
    CustomCodeHelpersTransformer = "CustomCodeHelpersTransformer",
    DeadCodeInjectionIdentifiersTransformer = "DeadCodeInjectionIdentifiersTransformer",
    DeadCodeInjectionTransformer = "DeadCodeInjectionTransformer",
    DirectivePlacementTransformer = "DirectivePlacementTransformer",
    EscapeSequenceTransformer = "EscapeSequenceTransformer",
    EvalCallExpressionTransformer = "EvalCallExpressionTransformer",
    ExportSpecifierTransformer = "ExportSpecifierTransformer",
    ExpressionStatementsMergeTransformer = "ExpressionStatementsMergeTransformer",
    FunctionControlFlowTransformer = "FunctionControlFlowTransformer",
    IfStatementSimplifyTransformer = "IfStatementSimplifyTransformer",
    LabeledStatementTransformer = "LabeledStatementTransformer",
    MemberExpressionTransformer = "MemberExpressionTransformer",
    MetadataTransformer = "MetadataTransformer",
    NumberLiteralTransformer = "NumberLiteralTransformer",
    NumberToNumericalExpressionTransformer = "NumberToNumericalExpressionTransformer",
    ObfuscatingGuardsTransformer = "ObfuscatingGuardsTransformer",
    ObjectExpressionKeysTransformer = "ObjectExpressionKeysTransformer",
    ObjectExpressionTransformer = "ObjectExpressionTransformer",
    ObjectPatternPropertiesTransformer = "ObjectPatternPropertiesTransformer",
    ParentificationTransformer = "ParentificationTransformer",
    RenamePropertiesTransformer = "RenamePropertiesTransformer",
    ScopeIdentifiersTransformer = "ScopeIdentifiersTransformer",
    ScopeThroughIdentifiersTransformer = "ScopeThroughIdentifiersTransformer",
    SplitStringTransformer = "SplitStringTransformer",
    StringArrayControlFlowTransformer = "StringArrayControlFlowTransformer",
    StringArrayTransformer = "StringArrayTransformer",
    StringArrayRotateFunctionTransformer = "StringArrayRotateFunctionTransformer",
    StringArrayScopeCallsWrapperTransformer = "StringArrayScopeCallsWrapperTransformer",
    TemplateLiteralTransformer = "TemplateLiteralTransformer",
    VariableDeclarationsMergeTransformer = "VariableDeclarationsMergeTransformer",
    VariablePreserveTransformer = "VariablePreserveTransformer"
}
