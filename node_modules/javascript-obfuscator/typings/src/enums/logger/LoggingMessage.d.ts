export declare enum LoggingMessage {
    EmptySourceCode = "Empty source code. Obfuscation canceled...",
    ObfuscationCompleted = "Obfuscation completed. Total time: %s sec.",
    ObfuscationStarted = "Obfuscation started...",
    RandomGeneratorSeed = "Random generator seed: %s...",
    CodeTransformationStage = "Code transformation stage: %s...",
    NodeTransformationStage = "AST transformation stage: %s...",
    Version = "Version: %s"
}
