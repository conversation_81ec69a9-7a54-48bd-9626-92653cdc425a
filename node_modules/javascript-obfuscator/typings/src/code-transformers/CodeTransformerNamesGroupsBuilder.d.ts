import { ICodeTransformer } from '../interfaces/code-transformers/ICodeTransformer';
import { CodeTransformer } from '../enums/code-transformers/CodeTransformer';
import { AbstractTransformerNamesGroupsBuilder } from '../utils/AbstractTransformerNamesGroupsBuilder';
export declare class CodeTransformerNamesGroupsBuilder extends AbstractTransformerNamesGroupsBuilder<CodeTransformer, ICodeTransformer> {
}
