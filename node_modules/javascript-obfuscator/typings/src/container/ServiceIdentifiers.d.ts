export declare enum ServiceIdentifiers {
    Factory__ICalleeDataExtractor = "Factory<ICalleeDataExtractor>",
    Factory__ICodeTransformer = "Factory<ICodeTransformer[]>",
    Factory__IControlFlowCustomNode = "Factory<IControlFlowCustomNode>",
    Factory__IControlFlowReplacer = "Factory<IControlFlowReplacer>",
    Factory__ICustomCodeHelper = "Factory<ICustomCodeHelper>",
    Factory__ICustomCodeHelperGroup = "Factory<ICustomCodeHelperGroup>",
    Factory__IDeadCodeInjectionCustomNode = "Factory<IDeadCodeInjectionCustomNode>",
    Factory__IIdentifierNamesGenerator = "Factory<IIdentifierNamesGenerator>",
    Factory__INodeGuard = "Factory<INodeGuard>",
    Factory__INodeTransformer = "Factory<INodeTransformer[]>",
    Factory__IObfuscationResult = "Factory<IObfuscationResult>",
    Factory__IObjectExpressionKeysTransformerCustomNode = "Factory<IObjectExpressionKeysTransformerCustomNode>",
    Factory__IObjectExpressionExtractor = "Factory<IObjectExpressionExtractor>",
    Factory__IStringArrayCustomNode = "Factory<IStringArrayCustomNode>",
    Factory__IStringArrayIndexNode = "Factory<IStringArrayIndexNode>",
    Factory__TControlFlowStorage = "Factory<TControlFlowStorage>",
    IArrayUtils = "IArrayUtils",
    ICalleeDataExtractor = "ICalleeDataExtractor",
    ICallsGraphAnalyzer = "ICallsGraphAnalyzer",
    ICodeTransformer = "ICodeTransformer",
    ICodeTransformerNamesGroupsBuilder = "ICodeTransformerNamesGroupsBuilder",
    ICodeTransformersRunner = "ICodeTransformersRunner",
    IControlFlowStorage = "IControlFlowStorage",
    ICryptUtils = "ICryptUtils",
    ICryptUtilsStringArray = "ICryptUtilsStringArray",
    ICustomCodeHelper = "ICustomCodeHelper",
    ICustomCodeHelperGroup = "ICustomCodeHelperGroup",
    IControlFlowReplacer = "IControlFlowReplacer",
    ICustomCodeHelperFormatter = "ICustomCodeHelperFormatter",
    ICustomCodeHelperObfuscator = "ICustomCodeHelperObfuscator",
    IEscapeSequenceEncoder = "IEscapeSequenceEncoder",
    IGlobalIdentifierNamesCacheStorage = "IGlobalIdentifierNamesCacheStorage",
    IIdentifierNamesGenerator = "IIdentifierNamesGenerator",
    IIdentifierReplacer = "IIdentifierReplacer",
    IJavaScriptObfuscator = "IJavaScriptObfuscator",
    ILevelledTopologicalSorter = "ILevelledTopologicalSorter",
    ILiteralNodesCacheStorage = "ILiteralNodesCacheStorage",
    ILogger = "ILogger",
    INodeGuard = "INodeGuard",
    INodeTransformer = "INodeTransformer",
    INodeTransformerNamesGroupsBuilder = "INodeTransformerNamesGroupsBuilder",
    INodeTransformersRunner = "INodeTransformersRunner",
    INumberNumericalExpressionAnalyzer = "INumberNumericalExpressionAnalyzer",
    IObfuscationResult = "IObfuscationResult",
    IOptions = "IOptions",
    IOptionsNormalizer = "IOptionsNormalizer",
    IPrevailingKindOfVariablesAnalyzer = "IPrevailingKindOfVariablesAnalyzer",
    IPropertyIdentifierNamesCacheStorage = "IPropertyIdentifierNamesCacheStorage",
    IObjectExpressionExtractor = "IObjectExpressionExtractor",
    IRandomGenerator = "IRandomGenerator",
    IRenamePropertiesReplacer = "IRenamePropertiesReplacer",
    IScopeIdentifiersTraverser = "IScopeIdentifiersTraverser",
    ISetUtils = "ISetUtils",
    ISourceCode = "ISourceCode",
    IScopeAnalyzer = "IScopeAnalyzer",
    IStringArrayIndexNode = "IStringArrayIndexNode",
    IStringArrayScopeCallsWrappersDataStorage = "IStringArrayScopeCallsWrappersDataStorage",
    IStringArrayStorage = "IStringArrayStorage",
    IStringArrayStorageAnalyzer = "IStringArrayStorageAnalyzer",
    IThroughIdentifierReplacer = "IThroughIdentifierReplacer",
    IVisitedLexicalScopeNodesStackStorage = "IVisitedLexicalScopeNodesStackStorage",
    Newable__ICustomNode = "Newable<ICustomNode>",
    TCustomNodeGroupStorage = "TCustomNodeGroupStorage",
    TInputOptions = "TInputOptions"
}
