import * as ESTree from 'estree';
import { TStatement } from '../types/node/TStatement';
export declare class NodeFactory {
    static programNode(body?: TStatement[]): ESTree.Program;
    static arrayExpressionNode(elements?: (ESTree.Expression | ESTree.SpreadElement)[]): ESTree.ArrayExpression;
    static assignmentExpressionNode(operator: ESTree.AssignmentOperator, left: ESTree.Pattern | ESTree.MemberExpression, right: ESTree.Expression): ESTree.AssignmentExpression;
    static binaryExpressionNode(operator: ESTree.BinaryOperator, left: ESTree.Expression, right: ESTree.Expression): ESTree.BinaryExpression;
    static blockStatementNode(body?: ESTree.Statement[]): ESTree.BlockStatement;
    static breakStatement(label?: ESTree.Identifier): ESTree.BreakStatement;
    static callExpressionNode(callee: ESTree.Expression, args?: (ESTree.Expression | ESTree.SpreadElement)[], optional?: boolean): ESTree.CallExpression;
    static conditionalExpressionNode(test: ESTree.Expression, consequent: ESTree.Expression, alternate: ESTree.Expression): ESTree.ConditionalExpression;
    static continueStatement(label?: ESTree.Identifier): ESTree.ContinueStatement;
    static directiveNode(expression: ESTree.Literal, directive: string): ESTree.Directive;
    static doWhileStatementNode(body: ESTree.Statement, test: ESTree.Expression): ESTree.DoWhileStatement;
    static exportAllDeclarationNode(source: ESTree.Literal, exported: ESTree.Identifier | null): ESTree.ExportAllDeclaration;
    static exportNamedDeclarationNode(specifiers: ESTree.ExportSpecifier[], source: ESTree.Literal): ESTree.ExportNamedDeclaration;
    static expressionStatementNode(expression: ESTree.Expression): ESTree.ExpressionStatement;
    static forStatementNode(init: ESTree.VariableDeclaration | ESTree.Expression | null, test: ESTree.Expression | null, update: ESTree.Expression | null, body: ESTree.Statement): ESTree.ForStatement;
    static forInStatementNode(left: ESTree.VariableDeclaration | ESTree.Pattern, right: ESTree.Expression, body: ESTree.Statement): ESTree.ForInStatement;
    static forOfStatementNode(asAwait: boolean, left: ESTree.VariableDeclaration | ESTree.Pattern, right: ESTree.Expression, body: ESTree.Statement): ESTree.ForOfStatement;
    static functionDeclarationNode(functionName: string, params: ESTree.Identifier[], body: ESTree.BlockStatement): ESTree.FunctionDeclaration;
    static functionExpressionNode(params: ESTree.Pattern[], body: ESTree.BlockStatement): ESTree.FunctionExpression;
    static ifStatementNode(test: ESTree.Expression, consequent: ESTree.Statement, alternate?: ESTree.Statement | null): ESTree.IfStatement;
    static identifierNode(name: string): ESTree.Identifier;
    static importDeclarationNode(specifiers: (ESTree.ImportSpecifier | ESTree.ImportDefaultSpecifier | ESTree.ImportNamespaceSpecifier)[], source: ESTree.Literal): ESTree.ImportDeclaration;
    static labeledStatementNode(label: ESTree.Identifier, body: ESTree.Statement): ESTree.LabeledStatement;
    static literalNode(value: boolean | number | string, raw?: string): ESTree.Literal;
    static logicalExpressionNode(operator: ESTree.LogicalOperator, left: ESTree.Expression, right: ESTree.Expression): ESTree.LogicalExpression;
    static memberExpressionNode(object: ESTree.Expression | ESTree.Super, property: ESTree.Expression, computed?: boolean, optional?: boolean): ESTree.MemberExpression;
    static objectExpressionNode(properties: (ESTree.Property | ESTree.SpreadElement)[]): ESTree.ObjectExpression;
    static propertyNode(key: ESTree.Expression, value: ESTree.Expression | ESTree.Pattern, computed?: boolean): ESTree.Property;
    static restElementNode(argument: ESTree.Pattern): ESTree.RestElement;
    static returnStatementNode(argument: ESTree.Expression): ESTree.ReturnStatement;
    static sequenceExpressionNode(expressions: ESTree.Expression[]): ESTree.SequenceExpression;
    static spreadElementNode(argument: ESTree.Expression): ESTree.SpreadElement;
    static staticBlockNode(body?: ESTree.Statement[]): ESTree.StaticBlock;
    static switchStatementNode(discriminant: ESTree.Expression, cases: ESTree.SwitchCase[]): ESTree.SwitchStatement;
    static switchCaseNode(test: ESTree.Expression, consequent: ESTree.Statement[]): ESTree.SwitchCase;
    static unaryExpressionNode(operator: ESTree.UnaryOperator, argument: ESTree.Expression, prefix?: true): ESTree.UnaryExpression;
    static updateExpressionNode(operator: ESTree.UpdateOperator, argumentExpr: ESTree.Expression): ESTree.UpdateExpression;
    static variableDeclarationNode(declarations?: ESTree.VariableDeclarator[], kind?: 'var' | 'let' | 'const'): ESTree.VariableDeclaration;
    static variableDeclaratorNode(id: ESTree.Identifier, init: ESTree.Expression | null): ESTree.VariableDeclarator;
    static whileStatementNode(test: ESTree.Expression, body: ESTree.Statement): ESTree.WhileStatement;
}
