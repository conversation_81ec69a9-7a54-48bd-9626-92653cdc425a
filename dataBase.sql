
CREATE TABLE users (
  id SERIAL PRIMARY KEY,
  key TEXT UNIQUE NOT NULL,
  hwid TEXT,
  session_id TEXT NOT NULL,
  ip_hash TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + interval '24 hours')
);

CREATE TABLE user_progression (
  id SERIAL PRIMARY KEY,
  token TEXT UNIQUE NOT NULL,
  current_step INTEGER DEFAULT 0,
  ip_hash TEXT,
  link_visit_time TIMESTAMP WITH TIME ZONE,
  selected_service VARCHAR(50),
  verification_metadata JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + interval '30 minutes')
);

CREATE TABLE rate_limits (
  id SERIAL PRIMARY KEY,
  session_id TEXT,
  ip_hash TEXT NOT NULL,
  action TEXT NOT NULL,
  attempts INTEGER DEFAULT 1,
  reset_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + interval '1 hour'),
  UNIQUE(ip_hash, action)
);

CREATE INDEX idx_users_ip_hash ON users(ip_hash);
CREATE INDEX idx_users_created_at ON users(created_at);
CREATE INDEX idx_user_progression_ip_hash ON user_progression(ip_hash);
CREATE INDEX idx_user_progression_link_visit ON user_progression(link_visit_time);
CREATE INDEX idx_user_progression_service ON user_progression(selected_service);
CREATE INDEX idx_rate_limits_ip_action ON rate_limits(ip_hash, action);

CREATE OR REPLACE VIEW suspicious_activity AS
SELECT
    token,
    ip_hash,
    selected_service,
    link_visit_time,
    created_at,
    updated_at,
    current_step,
    verification_metadata,
    EXTRACT(EPOCH FROM (updated_at - COALESCE(link_visit_time, created_at))) as time_spent_seconds,
    CASE
        WHEN current_step < 3 THEN 'INCOMPLETE_STEPS'
        WHEN EXTRACT(EPOCH FROM (updated_at - COALESCE(link_visit_time, created_at))) < 8 THEN 'TOO_FAST'
        WHEN verification_metadata->>'referrer' ILIKE '%bypass%' THEN 'BYPASS_REFERRER'
        WHEN verification_metadata->>'referrer' ILIKE '%bypasscity%' THEN 'BYPASS_CITY'
        WHEN verification_metadata->>'referrer' ILIKE '%direct-link%' THEN 'DIRECT_LINK'
        WHEN verification_metadata->>'mouseInteractions' = '0' THEN 'NO_INTERACTION'
        WHEN (verification_metadata->>'timeSpent')::integer < 8000 THEN 'INSUFFICIENT_TIME'
        ELSE 'NORMAL'
    END as suspicion_level,
    verification_metadata->>'referrer' as referrer,
    verification_metadata->>'userAgent' as user_agent,
    (verification_metadata->>'mouseInteractions')::integer as mouse_interactions,
    (verification_metadata->>'timeSpent')::integer as time_spent_ms
FROM user_progression
WHERE current_step >= 2
ORDER BY created_at DESC;

GRANT SELECT ON suspicious_activity TO authenticated;
GRANT SELECT ON suspicious_activity TO anon;

COMMENT ON COLUMN user_progression.link_visit_time IS 'Timestamp when user visited the link shortener';
COMMENT ON COLUMN user_progression.selected_service IS 'Which link shortener service was selected (linkvertise/shrinkme)';
COMMENT ON COLUMN user_progression.verification_metadata IS 'JSON data containing verification details (referrer, user agent, interaction data)';

INSERT INTO rate_limits (ip_hash, action, attempts, reset_at) VALUES
('example_ip_hash', 'link_verification', 0, NOW() + interval '5 minutes'),
('example_ip_hash', 'key_generation', 0, NOW() + interval '24 hours')
ON CONFLICT (ip_hash, action) DO NOTHING;
