const crypto = require('crypto');
const { verifyRecaptcha, verifySecureToken, generateKey } = require('../utils/auth');
const { hasGeneratedKeyToday, getTokenData, storeKey, deleteToken, getKeyData, update<PERSON>eyHwid, reset<PERSON>eyHwid, getTodaysKey, checkRateLimit } = require('../utils/database');
const { createResponse, getClientIP, hashIP } = require('../utils/security');

async function generateKeyHandler(event) {
  try {
    const { token, recaptchaResponse, skipRecaptcha } = JSON.parse(event.body);
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);
    const sessionId = crypto.randomBytes(16).toString('hex');


    const rateLimitPassed = await checkRateLimit(sessionId, ipHash, 'key_generation');
    if (!rateLimitPassed) {

      return createResponse(429, { error: 'Too many key generation attempts. Please wait before trying again.' });
    }

    if (!skipRecaptcha) {
      const isValid = await verifyRecaptcha(recaptchaResponse);

      if (!isValid) {
        return createResponse(400, { error: 'Invalid captcha' });
      }
    }

    const alreadyGeneratedToday = await hasGeneratedKeyToday(ipHash);
    if (alreadyGeneratedToday) {
      return createResponse(400, { error: 'You can only generate one key per day. Please try again tomorrow.' });
    }
    
    const decodedToken = verifySecureToken(token);
    if (!decodedToken) {
      return createResponse(400, { error: 'Invalid or expired token' });
    }
    
    const tokenId = decodedToken.tokenId;
    
    const { data: tokenData, error } = await getTokenData(tokenId);
    
    if (error) {

      return createResponse(400, { error: 'Invalid token' });
    }
    
    if (!tokenData) {

      return createResponse(400, { error: 'Invalid token' });
    }
    
    if (tokenData.current_step < 3) {
      return createResponse(400, { error: 'Please complete both link verification steps first' });
    }

    const key = generateKey();
    
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24);
    
    const { error: insertError } = await storeKey(key, sessionId, ipHash, expiresAt);
    
    if (insertError) {

      return createResponse(500, { error: 'Failed to store key. Please try again.' });
    }
    
    await deleteToken(tokenId);
    
    return createResponse(200, { 
      key, 
      expires_at: expiresAt.toISOString(),
      message: 'Key generated successfully' 
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function validateKey(event) {
  try {
    const { key, hwid } = JSON.parse(event.body);
    
    if (!key || !hwid) {
      return createResponse(400, { error: 'Missing key or HWID' });
    }
    
    const { data: keyData, error } = await getKeyData(key);
    
    if (error || !keyData) {
      return createResponse(400, { error: 'Invalid key' });
    }
    
    if (keyData.expires_at && new Date(keyData.expires_at) < new Date()) {
      return createResponse(400, { 
        error: 'Key expired. Please generate a new key.',
        expired: true
      });
    }
    
    if (!keyData.hwid) {
      await updateKeyHwid(key, hwid);
      
      return createResponse(200, { 
        message: 'Key validated and HWID set',
        expires_at: keyData.expires_at
      });
    }
    
    if (keyData.hwid !== hwid) {
      return createResponse(400, { error: 'HWID mismatch. Please reset your HWID.' });
    }
    
    return createResponse(200, { 
      message: 'Key validated successfully',
      expires_at: keyData.expires_at
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function resetHWID(event) {
  try {
    const { key } = JSON.parse(event.body);
    
    const { data: keyData, error } = await getKeyData(key);
    
    if (error || !keyData) {
      return createResponse(400, { error: 'Invalid key' });
    }
    
    await resetKeyHwid(key);
    
    return createResponse(200, { message: 'HWID reset successfully' });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function checkKeyStatus(event) {
  try {
    const { key } = JSON.parse(event.body);
    
    if (!key) {
      return createResponse(400, { error: 'Missing key' });
    }
    
    const { data: keyData, error } = await getKeyData(key);
    
    if (error || !keyData) {
      return createResponse(400, { error: 'Invalid key' });
    }
    
    if (new Date(keyData.expires_at) < new Date()) {
      return createResponse(400, { error: 'Key expired. Please renew your key.' });
    }
    
    return createResponse(200, { 
      status: 'valid',
      expires_at: keyData.expires_at
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function canGenerateKey(event) {
  try {
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);
    
    const alreadyGeneratedToday = await hasGeneratedKeyToday(ipHash);
    
    if (alreadyGeneratedToday) {
      return createResponse(200, { 
        canGenerate: false,
        message: 'You have already generated a key today. Please try again tomorrow.'
      });
    }
    
    return createResponse(200, { 
      canGenerate: true,
      message: 'You can generate a new key today.'
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

async function getTodaysKeyHandler(event) {
  try {
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);
    
    const { data, error } = await getTodaysKey(ipHash);
    
    if (error || !data || data.length === 0) {
      return createResponse(404, { error: 'No key found for today' });
    }
    
    const keyData = data[0];
    
    if (new Date(keyData.expires_at) < new Date()) {
      return createResponse(400, { error: 'Key expired. Please generate a new key.' });
    }
    
    return createResponse(200, { 
      key: keyData.key,
      expires_at: keyData.expires_at
    });
  } catch (error) {

    return createResponse(500, { error: 'Server error. Please try again.' });
  }
}

module.exports = {
  generateKeyHandler,
  validateKey,
  resetHWID,
  checkKeyStatus,
  canGenerateKey,
  getTodaysKeyHandler
};
