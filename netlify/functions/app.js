const { getHeaders, getClientIP, hashIP } = require('./utils/security');
const { generateToken, trackLinkVisit } = require('./handlers/tokenHandler');
const { verifyStep } = require('./handlers/verificationHandler');
const {
  generateKeyHand<PERSON>,
  validate<PERSON><PERSON>,
  reset<PERSON><PERSON><PERSON>,
  check<PERSON>eyStatus,
  canGenerate<PERSON><PERSON>,
  getTodaysKeyHandler
} = require('./handlers/keyHandler');

async function debugInfo(event) {
  try {
    const ip = getClientIP(event);
    const ipHash = hashIP(ip);

    const debugData = {
      timestamp: new Date().toISOString(),
      ip: ip,
      ipHash: ipHash.substring(0, 16) + '...',
      headers: {
        'x-forwarded-for': event.headers['x-forwarded-for'],
        'x-real-ip': event.headers['x-real-ip'],
        'cf-connecting-ip': event.headers['cf-connecting-ip'],
        'user-agent': event.headers['user-agent']
      },
      requestContext: event.requestContext?.identity?.sourceIp,
      environment: {
        hasRecaptchaSecret: !!process.env.RECAPTCHA_SECRET_KEY,
        hasSupabaseUrl: !!process.env.SUPABASE_URL,
        hasSupabaseKey: !!process.env.SUPABASE_KEY,
        hasJwtSecret: !!process.env.JWT_SECRET
      }
    };

    return {
      statusCode: 200,
      headers: getHeaders(),
      body: JSON.stringify(debugData, null, 2)
    };
  } catch (error) {

    return {
      statusCode: 500,
      headers: getHeaders(),
      body: JSON.stringify({ error: 'Debug error' })
    };
  }
}

exports.handler = async (event, context) => {
  const path = event.path.replace('/.netlify/functions/app', '');
  const method = event.httpMethod;
  
  const headers = getHeaders();
  
  if (method === 'OPTIONS') {
    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({ message: 'CORS preflight successful' })
    };
  }
  
  try {
    if (path === '/generate-token' && method === 'POST') {
      return await generateToken(event);
    }

    if (path === '/verify-step' && method === 'POST') {
      return await verifyStep(event);
    }

    if (path === '/track-link-visit' && method === 'POST') {
      return await trackLinkVisit(event);
    }

    if (path === '/generate-key' && method === 'POST') {
      return await generateKeyHandler(event);
    }

    if (path === '/validate-key' && method === 'POST') {
      return await validateKey(event);
    }

    if (path === '/reset-hwid' && method === 'POST') {
      return await resetHWID(event);
    }

    if (path === '/check-key-status' && method === 'POST') {
      return await checkKeyStatus(event);
    }

    if (path === '/can-generate-key' && method === 'GET') {
      return await canGenerateKey(event);
    }

    if (path === '/get-todays-key' && method === 'GET') {
      return await getTodaysKeyHandler(event);
    }

    if (path === '/debug-info' && method === 'GET') {
      return await debugInfo(event);
    }

    // Simple key check for Lua scripts - just check if key exists in database
    if (method === 'GET' && event.queryStringParameters) {
      const { action, key, hwid } = event.queryStringParameters;

      if (action === 'check-key' && key && hwid) {
        console.log(`API: Checking key ${key} with HWID ${hwid}`);

        // Basic format validation
        if (!key.match(/^PL-[A-F0-9-]+$/)) {
          console.log('Invalid key format');
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ valid: false, message: 'Invalid key format' })
          };
        }

        try {
          // Direct database lookup - much simpler!
          const { checkKeyInDatabase } = require('./utils/database');
          const keyExists = await checkKeyInDatabase(key, hwid);

          console.log(`Key validation result: ${keyExists}`);

          if (keyExists) {
            return {
              statusCode: 200,
              headers,
              body: JSON.stringify({ valid: true, message: 'Key found and valid' })
            };
          } else {
            return {
              statusCode: 200,
              headers,
              body: JSON.stringify({ valid: false, message: 'Key not found or expired' })
            };
          }
        } catch (error) {
          console.log('API error:', error);
          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ valid: false, message: 'Database error' })
          };
        }
      }
    }

    // Handle admin stats requests (POST with JSON body)
    if (method === 'POST' && event.body) {
      try {
        const { action } = JSON.parse(event.body);

        if (action === 'get-total-keys') {
          const { supabase } = require('./utils/database');
          const { count } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true });

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ count: count || 0 })
          };
        }

        if (action === 'get-active-users') {
          const { supabase } = require('./utils/database');
          const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
          const { count } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true })
            .not('hwid', 'is', null)
            .gte('updated_at', yesterday);

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ count: count || 0 })
          };
        }

        if (action === 'get-success-rate') {
          const { supabase } = require('./utils/database');

          // Get total keys
          const { count: totalKeys } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true });

          // Get keys with HWID (used keys)
          const { count: usedKeys } = await supabase
            .from('users')
            .select('*', { count: 'exact', head: true })
            .not('hwid', 'is', null);

          const rate = totalKeys > 0 ? Math.round((usedKeys / totalKeys) * 100) : 0;

          return {
            statusCode: 200,
            headers,
            body: JSON.stringify({ rate })
          };
        }

      } catch (error) {
        console.log('Admin stats error:', error);
      }
    }

    return {
      statusCode: 404,
      headers,
      body: JSON.stringify({ error: 'Not found' })
    };

  } catch (error) {

    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({ error: 'Server error. Please try again.' })
    };
  }
};
