const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRateLimit(sessionId, ipHash = null, action = 'general') {
  // If no ipHash provided, use sessionId as fallback
  const queryHash = ipHash || sessionId;

  const { data, error } = await supabase
    .from('rate_limits')
    .select('*')
    .eq('ip_hash', queryHash)
    .eq('action', action)
    .single();

  if (error && error.code !== 'PGRST116') {
    console.error('Error checking rate limit:', error);
    return false;
  }

  if (!data) {
    const { error: insertError } = await supabase
      .from('rate_limits')
      .insert([{
        session_id: sessionId,
        ip_hash: queryHash,
        action: action,
        attempts: 1,
        reset_at: new Date(Date.now() + 3600000).toISOString()
      }]);

    if (insertError) {
      console.error('Error inserting rate limit:', insertError);
      return false;
    }

    return true;
  }

  if (new Date(data.reset_at) < new Date()) {
    const { error: updateError } = await supabase
      .from('rate_limits')
      .update({
        attempts: 1,
        reset_at: new Date(Date.now() + 3600000).toISOString()
      })
      .eq('ip_hash', queryHash)
      .eq('action', action);

    if (updateError) {
      console.error('Error updating rate limit:', updateError);
      return false;
    }

    return true;
  }

  if (data.attempts >= 10) {
    return false;
  }

  const { error: incrementError } = await supabase
    .from('rate_limits')
    .update({
      attempts: data.attempts + 1
    })
    .eq('ip_hash', queryHash)
    .eq('action', action);

  if (incrementError) {
    console.error('Error incrementing rate limit:', incrementError);
    return false;
  }

  return true;
}

async function hasGeneratedKeyToday(ipHash) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const { data, error } = await supabase
    .from('users')
    .select('created_at')
    .eq('ip_hash', ipHash)
    .gte('created_at', today.toISOString())
    .limit(1);
  
  if (error) {
    console.error('Error checking key generation history:', error);
    return false;
  }
  
  return data && data.length > 0;
}

async function storeToken(tokenId, expiresAt, ipHash = null) {
  console.log(`Storing token ${tokenId} with IP hash: ${ipHash ? ipHash.substring(0, 8) + '...' : 'none'}`);

  const tokenData = {
    token: tokenId,
    expires_at: expiresAt.toISOString(),
    current_step: 1
  };

  // Add IP hash if provided
  if (ipHash) {
    tokenData.ip_hash = ipHash;
  }

  const { data, error } = await supabase
    .from('user_progression')
    .insert([tokenData])
    .select();

  if (error) {
    console.error('Error storing token:', error);
  } else {
    console.log('Token stored successfully:', data);
  }

  return { data, error };
}

async function getTokenData(tokenId) {
  const { data, error } = await supabase
    .from('user_progression')
    .select('*')
    .eq('token', tokenId)
    .single();
  
  return { data, error };
}

async function updateTokenStep(tokenId, step, updateData = {}) {
  console.log(`Updating token ${tokenId} to step ${step} with data:`, updateData);

  const { data, error } = await supabase
    .from('user_progression')
    .update({
      current_step: step,
      updated_at: new Date().toISOString(),
      ...updateData
    })
    .eq('token', tokenId)
    .select();

  if (error) {
    console.error('Error updating token step:', error);
  } else {
    console.log('Token step updated successfully:', data);
  }

  return { data, error };
}

async function trackLinkVisit(tokenId, service) {
  const { error } = await supabase
    .from('user_progression')
    .update({ 
      link_visit_time: new Date().toISOString(),
      selected_service: service,
      updated_at: new Date().toISOString()
    })
    .eq('token', tokenId);
  
  return { error };
}

async function storeKey(key, sessionId, ipHash, expiresAt) {
  console.log(`Storing key for IP hash: ${ipHash}, expires at: ${expiresAt.toISOString()}`);

  const { data, error } = await supabase
    .from('users')
    .insert([{
      key,
      session_id: sessionId,
      ip_hash: ipHash,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      expires_at: expiresAt.toISOString()
    }])
    .select();

  if (error) {
    console.error('Error storing key:', error);
  } else {
    console.log('Key stored successfully:', data);
  }

  return { data, error };
}

async function deleteToken(tokenId) {
  const { error } = await supabase
    .from('user_progression')
    .delete()
    .eq('token', tokenId);
  
  return { error };
}

async function getKeyData(key) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('key', key)
    .single();
  
  return { data, error };
}

async function updateKeyHwid(key, hwid) {
  const { error } = await supabase
    .from('users')
    .update({ 
      hwid,
      updated_at: new Date().toISOString()
    })
    .eq('key', key);
  
  return { error };
}

async function resetKeyHwid(key) {
  const { error } = await supabase
    .from('users')
    .update({ 
      hwid: null,
      updated_at: new Date().toISOString()
    })
    .eq('key', key);
  
  return { error };
}

async function getTodaysKey(ipHash) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const { data, error } = await supabase
    .from('users')
    .select('key, expires_at')
    .eq('ip_hash', ipHash)
    .gte('created_at', today.toISOString())
    .order('created_at', { ascending: false })
    .limit(1);

  return { data, error };
}

// Simple function to check if key exists and is valid with HWID binding
async function checkKeyInDatabase(key, hwid) {
  console.log(`Checking key: ${key} with HWID: ${hwid}`);

  try {
    const { data, error } = await supabase
      .from('users')
      .select('key, expires_at, hwid')
      .eq('key', key)
      .single();

    console.log('Database query result:', { data, error });

    if (error || !data) {
      console.log('Key not found in database');
      return false; // Key doesn't exist
    }

    // Check if key has expired
    const now = new Date();
    const expiresAt = new Date(data.expires_at);

    console.log(`Key expires at: ${expiresAt}, Current time: ${now}`);

    if (expiresAt < now) {
      console.log('Key has expired');
      return false; // Key expired
    }

    // HWID binding logic
    console.log(`Current HWID in DB: ${data.hwid}, Provided HWID: ${hwid}`);

    if (data.hwid === null || data.hwid === '') {
      // First time using this key - bind it to this HWID
      console.log('Binding key to new HWID');
      const { error: updateError } = await supabase
        .from('users')
        .update({
          hwid: hwid,
          updated_at: new Date().toISOString()
        })
        .eq('key', key);

      console.log('HWID binding result:', { updateError });

      if (updateError) {
        console.log('Failed to bind HWID:', updateError);
        return false; // Failed to bind HWID
      }

      console.log('Key successfully bound to HWID');
      return true; // Key bound to this HWID successfully
    } else if (data.hwid === hwid) {
      // HWID matches - user is authorized
      console.log('HWID matches - access granted');
      return true;
    } else {
      // Different HWID - key is already bound to someone else
      console.log('HWID mismatch - access denied');
      return false;
    }

  } catch (error) {
    console.log('Database error:', error);
    return false;
  }
}

module.exports = {
  supabase,
  checkRateLimit,
  hasGeneratedKeyToday,
  storeToken,
  getTokenData,
  updateTokenStep,
  trackLinkVisit,
  storeKey,
  deleteToken,
  getKeyData,
  updateKeyHwid,
  resetKeyHwid,
  getTodaysKey,
  checkKeyInDatabase
};
