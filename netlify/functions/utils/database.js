const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function checkRateLimit(sessionId, ipHash = null, action = 'general') {
  const queryHash = ipHash || sessionId;

  const { data, error } = await supabase
    .from('rate_limits')
    .select('*')
    .eq('ip_hash', queryHash)
    .eq('action', action)
    .single();

  if (error && error.code !== 'PGRST116') {

    return false;
  }

  if (!data) {
    const { error: insertError } = await supabase
      .from('rate_limits')
      .insert([{
        session_id: sessionId,
        ip_hash: queryHash,
        action: action,
        attempts: 1,
        reset_at: new Date(Date.now() + 3600000).toISOString()
      }]);

    if (insertError) {

      return false;
    }

    return true;
  }

  if (new Date(data.reset_at) < new Date()) {
    const { error: updateError } = await supabase
      .from('rate_limits')
      .update({
        attempts: 1,
        reset_at: new Date(Date.now() + 3600000).toISOString()
      })
      .eq('ip_hash', queryHash)
      .eq('action', action);

    if (updateError) {

      return false;
    }

    return true;
  }

  if (data.attempts >= 10) {
    return false;
  }

  const { error: incrementError } = await supabase
    .from('rate_limits')
    .update({
      attempts: data.attempts + 1
    })
    .eq('ip_hash', queryHash)
    .eq('action', action);

  if (incrementError) {

    return false;
  }

  return true;
}

async function hasGeneratedKeyToday(ipHash) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const { data, error } = await supabase
    .from('users')
    .select('created_at')
    .eq('ip_hash', ipHash)
    .gte('created_at', today.toISOString())
    .limit(1);
  
  if (error) {

    return false;
  }
  
  return data && data.length > 0;
}

async function storeToken(tokenId, expiresAt, ipHash = null) {
  const tokenData = {
    token: tokenId,
    expires_at: expiresAt.toISOString(),
    current_step: 1
  };

  if (ipHash) {
    tokenData.ip_hash = ipHash;
  }

  const { data, error } = await supabase
    .from('user_progression')
    .insert([tokenData])
    .select();



  return { data, error };
}

async function getTokenData(tokenId) {
  const { data, error } = await supabase
    .from('user_progression')
    .select('*')
    .eq('token', tokenId)
    .single();
  
  return { data, error };
}

async function updateTokenStep(tokenId, step, updateData = {}) {
  const { data, error } = await supabase
    .from('user_progression')
    .update({
      current_step: step,
      updated_at: new Date().toISOString(),
      ...updateData
    })
    .eq('token', tokenId)
    .select();

  return { data, error };
}

async function trackLinkVisit(tokenId, service) {
  const { error } = await supabase
    .from('user_progression')
    .update({ 
      link_visit_time: new Date().toISOString(),
      selected_service: service,
      updated_at: new Date().toISOString()
    })
    .eq('token', tokenId);
  
  return { error };
}

async function storeKey(key, sessionId, ipHash, expiresAt) {
  const { data, error } = await supabase
    .from('users')
    .insert([{
      key,
      session_id: sessionId,
      ip_hash: ipHash,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      expires_at: expiresAt.toISOString()
    }])
    .select();

  return { data, error };
}

async function deleteToken(tokenId) {
  const { error } = await supabase
    .from('user_progression')
    .delete()
    .eq('token', tokenId);
  
  return { error };
}

async function getKeyData(key) {
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('key', key)
    .single();
  
  return { data, error };
}

async function updateKeyHwid(key, hwid) {
  const { error } = await supabase
    .from('users')
    .update({ 
      hwid,
      updated_at: new Date().toISOString()
    })
    .eq('key', key);
  
  return { error };
}

async function resetKeyHwid(key) {
  const { error } = await supabase
    .from('users')
    .update({ 
      hwid: null,
      updated_at: new Date().toISOString()
    })
    .eq('key', key);
  
  return { error };
}

async function getTodaysKey(ipHash) {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const { data, error } = await supabase
    .from('users')
    .select('key, expires_at')
    .eq('ip_hash', ipHash)
    .gte('created_at', today.toISOString())
    .order('created_at', { ascending: false })
    .limit(1);
  
  return { data, error };
}

module.exports = {
  supabase,
  checkRateLimit,
  hasGeneratedKeyToday,
  storeToken,
  getTokenData,
  updateTokenStep,
  trackLinkVisit,
  storeKey,
  deleteToken,
  getKeyData,
  updateKeyHwid,
  resetKeyHwid,
  getTodaysKey
};
