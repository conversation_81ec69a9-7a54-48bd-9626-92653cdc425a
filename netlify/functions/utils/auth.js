const crypto = require('crypto');
const jwt = require('jsonwebtoken');

const JWT_SECRET = process.env.JWT_SECRET;

function generateSecureToken(payload) {
  return jwt.sign(payload, JWT_SECRET, { 
    expiresIn: '30m',
    jwtid: crypto.randomBytes(16).toString('hex')
  });
}

function verifySecureToken(token) {
  try {
    return jwt.verify(token, JWT_SECRET);
  } catch (error) {

    return null;
  }
}

async function verifyRecaptcha(recaptchaResponse) {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    return false;
  }

  if (!recaptchaResponse || typeof recaptchaResponse !== 'string' || recaptchaResponse.length < 10) {
    return false;
  }

  try {

    const response = await fetch('https://www.google.com/recaptcha/api/siteverify', {
      method: 'POST',
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      body: `secret=${secretKey}&response=${recaptchaResponse}`
    });

    if (!response.ok) {

      return false;
    }

    const data = await response.json();


    if (data['error-codes'] && data['error-codes'].length > 0) {
      return false;
    }

    return data.success === true;
  } catch (error) {

    return false;
  }
}

function generateToken() {
  return crypto.randomBytes(32).toString('hex');
}

function generateKey() {
  const prefix = "PL-";
  const segments = 3;
  const segmentLength = 5;
  
  let key = prefix;
  
  for (let i = 0; i < segments; i++) {
    if (i > 0) key += "-";
    key += crypto.randomBytes(segmentLength)
      .toString('hex')
      .toUpperCase()
      .substring(0, segmentLength);
  }
  
  return key;
}

module.exports = {
  generateSecureToken,
  verifySecureToken,
  verifyRecaptcha,
  generateToken,
  generateKey
};
