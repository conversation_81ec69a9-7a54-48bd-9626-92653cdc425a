const { supabase } = require('./database');

async function verifyLinkCompletion(tokenId, service, verificationData) {
  try {
    const { data: tokenData, error } = await supabase
      .from('user_progression')
      .select('*')
      .eq('token', tokenId)
      .single();
    
    if (error || !tokenData) {
      return { valid: false, reason: 'Invalid token' };
    }
    
    const timeOnLink = new Date() - new Date(tokenData.link_visit_time || tokenData.created_at);
    const minimumTime = 8000;

    if (timeOnLink < minimumTime) {
      return {
        valid: false,
        reason: `Please spend at least 8 seconds on the ${service} link. Time remaining: ${Math.ceil((minimumTime - timeOnLink) / 1000)}s`
      };
    }
    
    const validReferrers = {
      'linkvertise': ['linkvertise.com', 'link-center.net', 'link-target.net'],
      'shrinkme': ['shrinkme.io', 'shrinkme.org']
    };
    
    const referrer = verificationData.referrer || '';
    const serviceReferrers = validReferrers[service] || [];
    const hasValidReferrer = serviceReferrers.some(domain => referrer.includes(domain));
    
    const bypassIndicators = [
      'bypasscity.com',
      'bypass.city',
      'linkvertise.net',
      'shrink-me.io',
      'direct-link',
      'bypass'
    ];
    
    const hasBypassIndicator = bypassIndicators.some(indicator => 
      referrer.toLowerCase().includes(indicator) || 
      (verificationData.userAgent || '').toLowerCase().includes(indicator)
    );
    
    if (hasBypassIndicator) {
      return { 
        valid: false, 
        reason: 'Bypass detected. Please use the official link shortener.' 
      };
    }
    
    const ipHash = verificationData.ipHash;
    const recentVerifications = await supabase
      .from('user_progression')
      .select('created_at')
      .eq('ip_hash', ipHash)
      .gte('created_at', new Date(Date.now() - 300000).toISOString())
      .neq('token', tokenId);
    
    if (recentVerifications.data && recentVerifications.data.length > 2) {
      return { 
        valid: false, 
        reason: 'Too many verification attempts. Please wait 5 minutes.' 
      };
    }
    
    const interactionScore = calculateInteractionScore(verificationData);
    if (interactionScore < 3) {
      return { 
        valid: false, 
        reason: 'Insufficient interaction detected. Please properly complete the link.' 
      };
    }
    
    return { valid: true, reason: 'Link completion verified' };
    
  } catch (error) {

    return { valid: false, reason: 'Verification error' };
  }
}

function calculateInteractionScore(data) {
  let score = 0;

  const timeSpent = data.timeSpent || 0;
  score += Math.min(Math.floor(timeSpent / 3000), 3);

  if (data.hasValidReferrer) score += 2;

  if (data.mouseInteractions > 3) score += 1;

  const screenWidth = data.screenWidth || 0;
  const screenHeight = data.screenHeight || 0;
  if (screenWidth >= 800 && screenHeight >= 600 && screenWidth <= 3840 && screenHeight <= 2160) {
    score += 1;
  }

  const userAgent = data.userAgent || '';
  if (userAgent.includes('Chrome') || userAgent.includes('Firefox') || userAgent.includes('Safari')) {
    if (!userAgent.includes('Headless') && !userAgent.includes('PhantomJS')) {
      score += 1;
    }
  }

  return score;
}

function getHeaders() {
  return {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Content-Type': 'application/json'
  };
}

function createResponse(statusCode, body) {
  return {
    statusCode,
    headers: getHeaders(),
    body: JSON.stringify(body)
  };
}

function getClientIP(event) {
  const headers = event.headers || {};
  const ipHeaders = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded',
    'x-nf-client-connection-ip' // Netlify specific
  ];

  for (const header of ipHeaders) {
    const value = headers[header];
    if (value) {
      const ip = value.split(',')[0].trim();
      if (isValidIP(ip)) {
        return ip;
      }
    }
  }

  const remoteAddress = event.requestContext?.identity?.sourceIp;
  if (remoteAddress && isValidIP(remoteAddress)) {
    return remoteAddress;
  }

  const fallbackIP = '127.0.0.1';
  return fallbackIP;
}

function isValidIP(ip) {
  if (!ip || typeof ip !== 'string') return false;

  // Basic IP validation (IPv4 and IPv6)
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  // Allow common localhost variations and private IPs
  const validIPs = ['::1', 'localhost', '127.0.0.1'];

  return ipv4Regex.test(ip) || ipv6Regex.test(ip) || validIPs.includes(ip);
}

function hashIP(ip) {
  const crypto = require('crypto');

  const ipToHash = ip || 'unknown';
  const hash = crypto.createHash('sha256').update(ipToHash).digest('hex');
  return hash;
}

module.exports = {
  verifyLinkCompletion,
  calculateInteractionScore,
  getHeaders,
  createResponse,
  getClientIP,
  hashIP,
  isValidIP
};
