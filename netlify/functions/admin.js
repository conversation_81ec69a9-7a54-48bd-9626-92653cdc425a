// Admin API Endpoints
const { supabase } = require('./utils/database');
const { createResponse } = require('./utils/security');

// Admin authentication middleware
function verifyAdminAuth(event) {
  const authHeader = event.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return false;
  }
  
  const token = authHeader.substring(7);
  
  // Simple token validation (in production, use proper JWT verification)
  try {
    const decoded = atob(token);
    const [code, timestamp, role] = decoded.split(':');
    
    // Check if token is valid and not expired (24 hours)
    const now = Date.now();
    const tokenAge = now - parseInt(timestamp);
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    if (tokenAge > maxAge || role !== 'admin') {
      return false;
    }
    
    // Validate admin codes
    const validCodes = ['MADARA2024', 'ADMIN123', 'PROJECTL', 'KEYSYSTEM'];
    return validCodes.includes(code.toUpperCase());
  } catch (error) {
    return false;
  }
}

exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Content-Type': 'application/json'
  };

  // Handle preflight requests
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  // Verify admin authentication
  if (!verifyAdminAuth(event)) {
    return createResponse(401, { error: 'Unauthorized' });
  }

  const path = event.path.replace('/.netlify/functions/admin', '');
  const method = event.httpMethod;

  try {
    // Dashboard stats
    if (path === '/stats' && method === 'GET') {
      return await getStats();
    }

    // Recent keys
    if (path === '/recent-keys' && method === 'GET') {
      return await getRecentKeys();
    }

    // System status
    if (path === '/status/database' && method === 'GET') {
      return await checkDatabaseStatus();
    }

    if (path === '/status/api' && method === 'GET') {
      return await checkApiStatus();
    }

    if (path === '/status/keygen' && method === 'GET') {
      return await checkKeygenStatus();
    }

    // Key management
    if (path === '/keys' && method === 'GET') {
      return await getAllKeys();
    }

    if (path.startsWith('/keys/') && method === 'DELETE') {
      const key = path.split('/')[2];
      return await revokeKey(key);
    }

    if (path.startsWith('/keys/') && path.endsWith('/extend') && method === 'POST') {
      const key = path.split('/')[2];
      const { hours } = JSON.parse(event.body);
      return await extendKey(key, hours);
    }

    // User management
    if (path === '/users' && method === 'GET') {
      return await getAllUsers();
    }

    if (path.startsWith('/users/') && path.endsWith('/ban') && method === 'POST') {
      const hwid = path.split('/')[2];
      return await banUser(hwid);
    }

    if (path.startsWith('/users/') && path.endsWith('/reset') && method === 'POST') {
      const hwid = path.split('/')[2];
      return await resetUserHwid(hwid);
    }

    // Logs
    if (path === '/logs' && method === 'GET') {
      return await getLogs();
    }

    // Settings
    if (path === '/settings' && method === 'POST') {
      const settings = JSON.parse(event.body);
      return await saveSettings(settings);
    }

    return createResponse(404, { error: 'Endpoint not found' });

  } catch (error) {
    console.error('Admin API error:', error);
    return createResponse(500, { error: 'Internal server error' });
  }
};

// Dashboard functions
async function getStats() {
  try {
    // Get total keys
    const { count: totalKeys } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true });

    // Get active users (last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
    const { count: activeUsers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('updated_at', yesterday);

    // Calculate success rate (keys with HWID vs total)
    const { count: boundKeys } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .not('hwid', 'is', null);

    const successRate = totalKeys > 0 ? Math.round((boundKeys / totalKeys) * 100) : 0;

    // Average completion time (mock data for now)
    const avgTime = 45;

    return createResponse(200, {
      totalKeys: totalKeys || 0,
      activeUsers: activeUsers || 0,
      successRate,
      avgTime
    });
  } catch (error) {
    console.error('Error getting stats:', error);
    return createResponse(500, { error: 'Failed to get stats' });
  }
}

async function getRecentKeys() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('key, created_at, hwid, expires_at')
      .order('created_at', { ascending: false })
      .limit(10);

    if (error) throw error;

    const recentKeys = data.map(key => ({
      key: key.key,
      created_at: key.created_at,
      status: getKeyStatus(key)
    }));

    return createResponse(200, recentKeys);
  } catch (error) {
    console.error('Error getting recent keys:', error);
    return createResponse(500, { error: 'Failed to get recent keys' });
  }
}

async function checkDatabaseStatus() {
  try {
    const { error } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    return createResponse(200, { online: !error });
  } catch (error) {
    return createResponse(200, { online: false });
  }
}

async function checkApiStatus() {
  return createResponse(200, { online: true });
}

async function checkKeygenStatus() {
  // Check if key generation is enabled (you can store this in a settings table)
  return createResponse(200, { enabled: true });
}

async function getAllKeys() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;

    return createResponse(200, data);
  } catch (error) {
    console.error('Error getting all keys:', error);
    return createResponse(500, { error: 'Failed to get keys' });
  }
}

async function getAllUsers() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('hwid, ip_hash, created_at, updated_at')
      .not('hwid', 'is', null);

    if (error) throw error;

    // Group by HWID and count keys
    const userMap = {};
    data.forEach(user => {
      if (!userMap[user.hwid]) {
        userMap[user.hwid] = {
          hwid: user.hwid,
          ip_hash: user.ip_hash,
          key_count: 0,
          last_active: user.updated_at,
          status: 'active'
        };
      }
      userMap[user.hwid].key_count++;
      if (new Date(user.updated_at) > new Date(userMap[user.hwid].last_active)) {
        userMap[user.hwid].last_active = user.updated_at;
      }
    });

    return createResponse(200, Object.values(userMap));
  } catch (error) {
    console.error('Error getting users:', error);
    return createResponse(500, { error: 'Failed to get users' });
  }
}

async function revokeKey(key) {
  try {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('key', key);

    if (error) throw error;

    return createResponse(200, { message: 'Key revoked successfully' });
  } catch (error) {
    console.error('Error revoking key:', error);
    return createResponse(500, { error: 'Failed to revoke key' });
  }
}

async function extendKey(key, hours) {
  try {
    const newExpiry = new Date(Date.now() + hours * 60 * 60 * 1000).toISOString();
    
    const { error } = await supabase
      .from('users')
      .update({ expires_at: newExpiry })
      .eq('key', key);

    if (error) throw error;

    return createResponse(200, { message: 'Key extended successfully' });
  } catch (error) {
    console.error('Error extending key:', error);
    return createResponse(500, { error: 'Failed to extend key' });
  }
}

async function banUser(hwid) {
  try {
    // Delete all keys for this HWID
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('hwid', hwid);

    if (error) throw error;

    return createResponse(200, { message: 'User banned successfully' });
  } catch (error) {
    console.error('Error banning user:', error);
    return createResponse(500, { error: 'Failed to ban user' });
  }
}

async function resetUserHwid(hwid) {
  try {
    const { error } = await supabase
      .from('users')
      .update({ hwid: null })
      .eq('hwid', hwid);

    if (error) throw error;

    return createResponse(200, { message: 'HWID reset successfully' });
  } catch (error) {
    console.error('Error resetting HWID:', error);
    return createResponse(500, { error: 'Failed to reset HWID' });
  }
}

async function getLogs() {
  // Mock logs for now - you can implement real logging later
  const mockLogs = [
    { timestamp: new Date().toISOString(), level: 'info', message: 'Admin dashboard accessed' },
    { timestamp: new Date(Date.now() - 60000).toISOString(), level: 'info', message: 'Key generated: PL-ABC123' },
    { timestamp: new Date(Date.now() - 120000).toISOString(), level: 'warning', message: 'Rate limit exceeded for IP: ***********' },
    { timestamp: new Date(Date.now() - 180000).toISOString(), level: 'info', message: 'User verification completed' },
    { timestamp: new Date(Date.now() - 240000).toISOString(), level: 'error', message: 'Database connection timeout' }
  ];

  return createResponse(200, mockLogs);
}

async function saveSettings(settings) {
  // Mock settings save - you can implement real settings storage later
  console.log('Saving settings:', settings);
  return createResponse(200, { message: 'Settings saved successfully' });
}

// Utility functions
function getKeyStatus(key) {
  const now = new Date();
  const expires = new Date(key.expires_at);
  
  if (expires < now) return 'expired';
  if (key.hwid) return 'active';
  return 'unused';
}
