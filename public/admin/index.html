<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Madara - Admin Dashboard</title>
    <link rel="stylesheet" href="styles/admin.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-shield-alt"></i> Admin Panel</h2>
                <p>Project Madara</p>
            </div>
            
            <ul class="sidebar-menu">
                <li class="active" data-section="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </li>
                <li data-section="keys">
                    <i class="fas fa-key"></i>
                    <span>Key Management</span>
                </li>
                <li data-section="users">
                    <i class="fas fa-users"></i>
                    <span>User Management</span>
                </li>
                <li data-section="analytics">
                    <i class="fas fa-chart-bar"></i>
                    <span>Analytics</span>
                </li>
                <li data-section="logs">
                    <i class="fas fa-file-alt"></i>
                    <span>System Logs</span>
                </li>
                <li data-section="settings">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </li>
            </ul>
            
            <div class="sidebar-footer">
                <button class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    Logout
                </button>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="admin-header">
                <div class="header-left">
                    <h1 id="page-title">Dashboard</h1>
                    <p id="page-subtitle">System Overview</p>
                </div>
                <div class="header-right">
                    <div class="admin-info">
                        <span>Welcome, Admin</span>
                        <div class="admin-avatar">
                            <i class="fas fa-user-shield"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard-section" class="content-section active">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-keys">Loading...</h3>
                            <p>Total Keys Generated</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="active-users">Loading...</h3>
                            <p>Active Users (24h)</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="success-rate">Loading...</h3>
                            <p>Success Rate</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="avg-time">Loading...</h3>
                            <p>Avg. Completion Time</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-grid">
                    <div class="dashboard-card">
                        <h3>Recent Key Generations</h3>
                        <div class="recent-keys" id="recent-keys">
                            <div class="loading">Loading recent keys...</div>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <h3>System Status</h3>
                        <div class="system-status">
                            <div class="status-item">
                                <span class="status-label">Database</span>
                                <span class="status-indicator online" id="db-status">Online</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">API Server</span>
                                <span class="status-indicator online" id="api-status">Online</span>
                            </div>
                            <div class="status-item">
                                <span class="status-label">Key Generation</span>
                                <span class="status-indicator online" id="keygen-status">Enabled</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Key Management Section -->
            <section id="keys-section" class="content-section">
                <div class="section-header">
                    <h2>Key Management</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="refresh-keys">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-danger" onclick="admin.cleanupExpiredKeys()">
                            <i class="fas fa-trash"></i> Clean Expired
                        </button>
                        <button class="btn btn-secondary" onclick="admin.addToBlacklist()">
                            <i class="fas fa-ban"></i> Add Blacklist
                        </button>
                    </div>
                </div>
                
                <div class="search-bar">
                    <input type="text" id="key-search" placeholder="Search keys...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="admin-table" id="keys-table">
                        <thead>
                            <tr>
                                <th>Key</th>
                                <th>HWID</th>
                                <th>Created</th>
                                <th>Expires</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="keys-tbody">
                            <tr>
                                <td colspan="6" class="loading">Loading keys...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- User Management Section -->
            <section id="users-section" class="content-section">
                <div class="section-header">
                    <h2>User Management</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="refresh-users">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                
                <div class="search-bar">
                    <input type="text" id="user-search" placeholder="Search by HWID or IP...">
                    <button class="search-btn">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="table-container">
                    <table class="admin-table" id="users-table">
                        <thead>
                            <tr>
                                <th>HWID</th>
                                <th>IP Hash</th>
                                <th>Keys Generated</th>
                                <th>Last Active</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="users-tbody">
                            <tr>
                                <td colspan="6" class="loading">Loading users...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Analytics Section -->
            <section id="analytics-section" class="content-section">
                <div class="section-header">
                    <h2>Analytics</h2>
                    <div class="section-actions">
                        <select id="analytics-period">
                            <option value="24h">Last 24 Hours</option>
                            <option value="7d">Last 7 Days</option>
                            <option value="30d">Last 30 Days</option>
                        </select>
                    </div>
                </div>
                
                <div class="analytics-grid">
                    <div class="chart-card">
                        <h3>Key Generation Over Time</h3>
                        <canvas id="keys-chart"></canvas>
                    </div>
                    
                    <div class="chart-card">
                        <h3>User Activity</h3>
                        <canvas id="activity-chart"></canvas>
                    </div>
                </div>
            </section>

            <!-- Logs Section -->
            <section id="logs-section" class="content-section">
                <div class="section-header">
                    <h2>System Logs</h2>
                    <div class="section-actions">
                        <button class="btn btn-primary" id="refresh-logs">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                        <button class="btn btn-secondary" id="clear-logs">
                            <i class="fas fa-trash"></i> Clear Logs
                        </button>
                    </div>
                </div>
                
                <div class="logs-container" id="logs-container">
                    <div class="loading">Loading logs...</div>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings-section" class="content-section">
                <div class="section-header">
                    <h2>System Settings</h2>
                </div>
                
                <div class="settings-grid">
                    <div class="setting-card">
                        <h3>Key Generation</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-keygen" checked>
                                Enable Key Generation
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>Key Duration (hours):</label>
                            <input type="number" id="key-duration" value="24" min="1" max="168">
                        </div>
                    </div>
                    
                    <div class="setting-card">
                        <h3>Security</h3>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-hwid" checked>
                                Enable HWID Binding
                            </label>
                        </div>
                        <div class="setting-item">
                            <label>
                                <input type="checkbox" id="enable-ratelimit" checked>
                                Enable Rate Limiting
                            </label>
                        </div>
                    </div>
                </div>
                
                <button class="btn btn-success" id="save-settings">
                    <i class="fas fa-save"></i> Save Settings
                </button>
            </section>
        </main>
    </div>

    <script src="scripts/admin.js"></script>
</body>
</html>
