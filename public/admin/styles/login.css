/* Modern Dark Admin Login */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #0a0a0a;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.login-container {
    position: relative;
    z-index: 10;
}

.login-card {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 24px;
    padding: 48px;
    width: 420px;
    backdrop-filter: blur(20px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
    animation: slideIn 0.6s ease-out;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #333333 50%, transparent 100%);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.logo {
    width: 72px;
    height: 72px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px;
    font-size: 1.8rem;
    color: white;
}

.login-header h1 {
    color: #ffffff;
    font-size: 1.8rem;
    margin-bottom: 8px;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.login-header p {
    color: #666666;
    font-size: 0.9rem;
    font-weight: 500;
}

.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    color: #ccc;
    margin-bottom: 8px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-group label i {
    color: #3b82f6;
    width: 16px;
}

.form-group input {
    width: 100%;
    padding: 16px 20px;
    background: #0a0a0a;
    border: 1px solid #1f1f1f;
    border-radius: 14px;
    color: white;
    font-size: 0.95rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #333333;
    background: #111111;
}

.form-group input::placeholder {
    color: #666666;
}

.login-btn {
    width: 100%;
    padding: 16px;
    background: #ffffff;
    color: #000000;
    border: none;
    border-radius: 14px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    letter-spacing: -0.01em;
}

.login-btn:hover {
    background: #f5f5f5;
    transform: translateY(-1px);
}

.login-btn:active {
    transform: translateY(0);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.login-info {
    text-align: center;
    margin-top: 20px;
    padding: 15px;
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.2);
    border-radius: 10px;
}

.login-info p {
    color: #4CAF50;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.login-footer {
    text-align: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #333;
}

.login-footer p {
    color: #666;
    font-size: 0.8rem;
}

/* Background Animation */
.background-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-icon {
    position: absolute;
    color: rgba(76, 175, 80, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.floating-icon:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.floating-icon:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
}

.floating-icon:nth-child(3) {
    top: 30%;
    left: 70%;
    animation-delay: 2s;
}

.floating-icon:nth-child(4) {
    top: 80%;
    left: 20%;
    animation-delay: 3s;
}

.floating-icon:nth-child(5) {
    top: 10%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* Error/Success Messages */
.message {
    padding: 12px 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.message.error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid rgba(244, 67, 54, 0.3);
    color: #f44336;
}

.message.success {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    color: #4CAF50;
}

/* Loading State */
.login-btn.loading {
    position: relative;
    color: transparent;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 480px) {
    .login-card {
        width: 90%;
        padding: 30px 20px;
        margin: 20px;
    }
    
    .logo {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
    
    .login-header h1 {
        font-size: 1.5rem;
    }
    
    .floating-icon {
        font-size: 1.5rem;
    }
}
