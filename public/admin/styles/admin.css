/* Modern Dark Admin Dashboard */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: #0a0a0a;
    color: #ffffff;
    overflow-x: hidden;
    line-height: 1.6;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Modern Sidebar */
.sidebar {
    width: 280px;
    background: #111111;
    border-right: 1px solid #1f1f1f;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
    backdrop-filter: blur(20px);
}

.sidebar-header {
    padding: 32px 24px;
    border-bottom: 1px solid #1f1f1f;
    text-align: center;
}

.sidebar-header h2 {
    color: #ffffff;
    margin-bottom: 8px;
    font-size: 1.4rem;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.sidebar-header p {
    color: #666666;
    font-size: 0.85rem;
    font-weight: 500;
}

.sidebar-menu {
    list-style: none;
    padding: 16px 0;
    flex: 1;
}

.sidebar-menu li {
    margin: 2px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 12px;
}

.sidebar-menu li:hover {
    background: #1a1a1a;
}

.sidebar-menu li.active {
    background: #1f1f1f;
    border: 1px solid #2a2a2a;
}

.sidebar-menu li a,
.sidebar-menu li {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    color: #999999;
    text-decoration: none;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.sidebar-menu li:hover {
    color: #ffffff;
}

.sidebar-menu li.active {
    color: #ffffff;
}

.sidebar-menu li i {
    margin-right: 12px;
    width: 18px;
    text-align: center;
    font-size: 0.9rem;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #3d4a7b;
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
}

.logout-btn:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

/* Modern Main Content */
.main-content {
    margin-left: 280px;
    flex: 1;
    background: #0a0a0a;
    min-height: 100vh;
}

.admin-header {
    background: #111111;
    padding: 24px 32px;
    border-bottom: 1px solid #1f1f1f;
    display: flex;
    justify-content: space-between;
    align-items: center;
    backdrop-filter: blur(20px);
}

.header-left h1 {
    color: #ffffff;
    margin-bottom: 4px;
    font-size: 1.75rem;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.header-left p {
    color: #666666;
    font-size: 0.9rem;
    font-weight: 500;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* Modern Content Sections */
.content-section {
    display: none;
    padding: 32px;
    animation: fadeIn 0.3s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Modern Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}

.stat-card {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 16px;
    padding: 28px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #ffffff 0%, #666666 100%);
}

.stat-card:hover {
    transform: translateY(-2px);
    border-color: #2a2a2a;
    background: #151515;
}

.stat-icon {
    width: 56px;
    height: 56px;
    background: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
    color: #ffffff;
    flex-shrink: 0;
}

.stat-info h3 {
    font-size: 2.2rem;
    color: #ffffff;
    margin-bottom: 4px;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.stat-info p {
    color: #666666;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Modern Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 24px;
}

.dashboard-card {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 16px;
    padding: 28px;
    position: relative;
    overflow: hidden;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #333333 50%, transparent 100%);
}

.dashboard-card h3 {
    color: #ffffff;
    margin-bottom: 24px;
    padding-bottom: 12px;
    border-bottom: 1px solid #1f1f1f;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: -0.01em;
}

/* Recent Keys */
.recent-keys {
    max-height: 300px;
    overflow-y: auto;
}

.key-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.key-item:last-child {
    border-bottom: none;
}

.key-info {
    flex: 1;
}

.key-code {
    font-family: 'Courier New', monospace;
    color: #4CAF50;
    font-weight: bold;
}

.key-time {
    color: #888;
    font-size: 0.8rem;
}

/* Modern System Status */
.system-status {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #1a1a1a;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    color: #cccccc;
    font-weight: 500;
    font-size: 0.85rem;
}

.status-indicator {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-indicator.online {
    background: #16a34a;
    color: white;
}

.status-indicator.offline {
    background: #dc2626;
    color: white;
}

/* Modern Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 32px;
    padding-bottom: 20px;
    border-bottom: 1px solid #1f1f1f;
}

.section-header h2 {
    color: #ffffff;
    font-size: 1.5rem;
    font-weight: 700;
    letter-spacing: -0.02em;
}

.section-actions {
    display: flex;
    gap: 12px;
}

/* Modern Buttons */
.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 600;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    letter-spacing: -0.01em;
}

.btn-primary {
    background: #ffffff;
    color: #000000;
    border: 1px solid #2a2a2a;
}

.btn-primary:hover {
    background: #f5f5f5;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #1a1a1a;
    color: #ffffff;
    border: 1px solid #2a2a2a;
}

.btn-secondary:hover {
    background: #222222;
    border-color: #333333;
}

.btn-danger {
    background: #dc2626;
    color: white;
    border: 1px solid #dc2626;
}

.btn-danger:hover {
    background: #b91c1c;
    border-color: #b91c1c;
    transform: translateY(-1px);
}

.btn-success {
    background: #16a34a;
    color: white;
    margin-top: 20px;
    border: 1px solid #16a34a;
}

.btn-success:hover {
    background: #15803d;
    border-color: #15803d;
    transform: translateY(-1px);
}

/* Modern Search Bar */
.search-bar {
    display: flex;
    margin-bottom: 24px;
    gap: 12px;
}

.search-bar input {
    flex: 1;
    padding: 14px 16px;
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 12px;
    color: white;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.search-bar input:focus {
    outline: none;
    border-color: #333333;
    background: #151515;
}

.search-bar input::placeholder {
    color: #666666;
}

.search-btn {
    padding: 14px 20px;
    background: #1a1a1a;
    color: white;
    border: 1px solid #2a2a2a;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.search-btn:hover {
    background: #222222;
    border-color: #333333;
}

/* Modern Tables */
.table-container {
    background: #111111;
    border: 1px solid #1f1f1f;
    border-radius: 16px;
    overflow: hidden;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid #1f1f1f;
    font-size: 0.85rem;
}

.admin-table th {
    background: #0a0a0a;
    color: #ffffff;
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.admin-table tr:hover {
    background: #151515;
}

.admin-table code {
    background: #1a1a1a;
    padding: 4px 8px;
    border-radius: 6px;
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    font-size: 0.8rem;
    border: 1px solid #2a2a2a;
}

/* Loading */
.loading {
    text-align: center;
    color: #888;
    padding: 20px;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.setting-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
}

.setting-card h3 {
    color: #4CAF50;
    margin-bottom: 20px;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item label {
    display: block;
    margin-bottom: 5px;
    color: #ccc;
}

.setting-item input[type="checkbox"] {
    margin-right: 10px;
}

.setting-item input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    background: #2d2d2d;
    border: 1px solid #333;
    border-radius: 6px;
    color: white;
}

/* Logs */
.logs-container {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 20px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
}

.log-entry.info {
    background: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196F3;
}

.log-entry.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #FFC107;
}

.log-entry.error {
    background: rgba(244, 67, 54, 0.1);
    border-left: 3px solid #f44336;
}

/* Charts */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
}

.chart-card h3 {
    color: #4CAF50;
    margin-bottom: 20px;
}

.chart-card canvas {
    max-width: 100%;
    height: 300px;
}

/* Action Buttons */
.btn-sm {
    padding: 8px 12px;
    font-size: 0.8rem;
    margin-right: 5px;
    border-radius: 6px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
}

/* Badge */
.badge {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.key-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.key-status.active {
    background: #4CAF50;
    color: white;
}

.key-status.expired {
    background: #f44336;
    color: white;
}

.key-status.unused {
    background: #FF9800;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .search-bar {
        flex-direction: column;
    }
}
