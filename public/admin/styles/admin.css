/* Admin Dashboard Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #0f0f0f;
    color: #ffffff;
    overflow-x: hidden;
}

.admin-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border-right: 1px solid #333;
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 1000;
}

.sidebar-header {
    padding: 30px 20px;
    border-bottom: 1px solid #333;
    text-align: center;
}

.sidebar-header h2 {
    color: #4CAF50;
    margin-bottom: 5px;
    font-size: 1.5rem;
}

.sidebar-header p {
    color: #888;
    font-size: 0.9rem;
}

.sidebar-menu {
    list-style: none;
    padding: 20px 0;
    flex: 1;
}

.sidebar-menu li {
    margin: 5px 0;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sidebar-menu li:hover,
.sidebar-menu li.active {
    background: rgba(76, 175, 80, 0.1);
    border-right: 3px solid #4CAF50;
}

.sidebar-menu li a,
.sidebar-menu li {
    display: flex;
    align-items: center;
    padding: 15px 25px;
    color: #ccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar-menu li:hover,
.sidebar-menu li.active {
    color: #4CAF50;
}

.sidebar-menu li i {
    margin-right: 15px;
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid #333;
}

.logout-btn {
    width: 100%;
    padding: 12px;
    background: #f44336;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.logout-btn:hover {
    background: #d32f2f;
}

/* Main Content */
.main-content {
    margin-left: 280px;
    flex: 1;
    background: #0f0f0f;
}

.admin-header {
    background: #1a1a1a;
    padding: 25px 30px;
    border-bottom: 1px solid #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left h1 {
    color: #4CAF50;
    margin-bottom: 5px;
}

.header-left p {
    color: #888;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-avatar {
    width: 40px;
    height: 40px;
    background: #4CAF50;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

/* Content Sections */
.content-section {
    display: none;
    padding: 30px;
}

.content-section.active {
    display: block;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(76, 175, 80, 0.2);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: #4CAF50;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-info h3 {
    font-size: 2rem;
    color: #4CAF50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #888;
    font-size: 0.9rem;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
}

.dashboard-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
}

.dashboard-card h3 {
    color: #4CAF50;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #333;
}

/* Recent Keys */
.recent-keys {
    max-height: 300px;
    overflow-y: auto;
}

.key-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #333;
}

.key-item:last-child {
    border-bottom: none;
}

.key-info {
    flex: 1;
}

.key-code {
    font-family: 'Courier New', monospace;
    color: #4CAF50;
    font-weight: bold;
}

.key-time {
    color: #888;
    font-size: 0.8rem;
}

/* System Status */
.system-status {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
}

.status-indicator {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-indicator.online {
    background: #4CAF50;
    color: white;
}

.status-indicator.offline {
    background: #f44336;
    color: white;
}

/* Section Header */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #333;
}

.section-header h2 {
    color: #4CAF50;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background: #45a049;
}

.btn-secondary {
    background: #666;
    color: white;
}

.btn-secondary:hover {
    background: #555;
}

.btn-danger {
    background: #f44336;
    color: white;
}

.btn-danger:hover {
    background: #d32f2f;
}

.btn-success {
    background: #4CAF50;
    color: white;
    margin-top: 20px;
}

/* Search Bar */
.search-bar {
    display: flex;
    margin-bottom: 20px;
    gap: 10px;
}

.search-bar input {
    flex: 1;
    padding: 12px 15px;
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    color: white;
    font-size: 0.9rem;
}

.search-bar input:focus {
    outline: none;
    border-color: #4CAF50;
}

.search-btn {
    padding: 12px 20px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

/* Tables */
.table-container {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    overflow: hidden;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #333;
}

.admin-table th {
    background: #2d2d2d;
    color: #4CAF50;
    font-weight: bold;
}

.admin-table tr:hover {
    background: rgba(76, 175, 80, 0.05);
}

/* Loading */
.loading {
    text-align: center;
    color: #888;
    padding: 20px;
}

/* Settings */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.setting-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
}

.setting-card h3 {
    color: #4CAF50;
    margin-bottom: 20px;
}

.setting-item {
    margin-bottom: 15px;
}

.setting-item label {
    display: block;
    margin-bottom: 5px;
    color: #ccc;
}

.setting-item input[type="checkbox"] {
    margin-right: 10px;
}

.setting-item input[type="number"] {
    width: 100%;
    padding: 8px 12px;
    background: #2d2d2d;
    border: 1px solid #333;
    border-radius: 6px;
    color: white;
}

/* Logs */
.logs-container {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 20px;
    height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

.log-entry {
    margin-bottom: 10px;
    padding: 8px;
    border-radius: 4px;
}

.log-entry.info {
    background: rgba(33, 150, 243, 0.1);
    border-left: 3px solid #2196F3;
}

.log-entry.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left: 3px solid #FFC107;
}

.log-entry.error {
    background: rgba(244, 67, 54, 0.1);
    border-left: 3px solid #f44336;
}

/* Charts */
.analytics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.chart-card {
    background: #1a1a1a;
    border: 1px solid #333;
    border-radius: 12px;
    padding: 25px;
}

.chart-card h3 {
    color: #4CAF50;
    margin-bottom: 20px;
}

.chart-card canvas {
    max-width: 100%;
    height: 300px;
}

/* Action Buttons */
.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
    margin-right: 5px;
}

.key-status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: bold;
}

.key-status.active {
    background: #4CAF50;
    color: white;
}

.key-status.expired {
    background: #f44336;
    color: white;
}

.key-status.unused {
    background: #FF9800;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
    }

    .main-content {
        margin-left: 0;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .analytics-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .search-bar {
        flex-direction: column;
    }
}
