// Admin Dashboard JavaScript
class AdminDashboard {
    constructor() {
        this.currentSection = 'dashboard';
        this.init();
    }

    init() {
        // Check authentication first
        if (!this.checkAuth()) {
            window.location.href = '/admin/login.html';
            return;
        }

        this.setupEventListeners();
        this.loadDashboardData();
        this.startAutoRefresh();
    }

    checkAuth() {
        const token = localStorage.getItem('admin_token');
        const authTime = localStorage.getItem('admin_auth_time');

        if (!token || !authTime) {
            return false;
        }

        const now = Date.now();
        const authAge = now - parseInt(authTime);
        const maxAge = 24 * 60 * 60 * 1000; // 24 hours

        if (authAge > maxAge) {
            localStorage.removeItem('admin_token');
            localStorage.removeItem('admin_auth_time');
            return false;
        }

        return true;
    }

    setupEventListeners() {
        // Sidebar navigation
        document.querySelectorAll('.sidebar-menu li').forEach(item => {
            item.addEventListener('click', (e) => {
                const section = e.currentTarget.dataset.section;
                this.switchSection(section);
            });
        });

        // Refresh buttons
        document.getElementById('refresh-keys')?.addEventListener('click', () => this.loadKeys());
        document.getElementById('refresh-users')?.addEventListener('click', () => this.loadUsers());
        document.getElementById('refresh-logs')?.addEventListener('click', () => this.loadLogs());

        // Search functionality
        document.getElementById('key-search')?.addEventListener('input', (e) => this.searchKeys(e.target.value));
        document.getElementById('user-search')?.addEventListener('input', (e) => this.searchUsers(e.target.value));

        // Settings
        document.getElementById('save-settings')?.addEventListener('click', () => this.saveSettings());

        // Logout
        document.querySelector('.logout-btn')?.addEventListener('click', () => this.logout());
    }

    switchSection(section) {
        // Update sidebar
        document.querySelectorAll('.sidebar-menu li').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${section}"]`).classList.add('active');

        // Update content
        document.querySelectorAll('.content-section').forEach(section => {
            section.classList.remove('active');
        });
        document.getElementById(`${section}-section`).classList.add('active');

        // Update header
        const titles = {
            dashboard: { title: 'Dashboard', subtitle: 'System Overview' },
            keys: { title: 'Key Management', subtitle: 'Manage Generated Keys' },
            users: { title: 'User Management', subtitle: 'Manage Users and HWIDs' },
            analytics: { title: 'Analytics', subtitle: 'System Performance' },
            logs: { title: 'System Logs', subtitle: 'Monitor System Activity' },
            settings: { title: 'Settings', subtitle: 'System Configuration' }
        };

        document.getElementById('page-title').textContent = titles[section].title;
        document.getElementById('page-subtitle').textContent = titles[section].subtitle;

        // Load section data
        this.loadSectionData(section);
        this.currentSection = section;
    }

    async loadSectionData(section) {
        switch (section) {
            case 'dashboard':
                await this.loadDashboardData();
                break;
            case 'keys':
                await this.loadKeys();
                break;
            case 'users':
                await this.loadUsers();
                break;
            case 'analytics':
                await this.loadAnalytics();
                break;
            case 'logs':
                await this.loadLogs();
                break;
            case 'settings':
                await this.loadSettings();
                break;
        }
    }

    async loadDashboardData() {
        try {
            // Load stats
            const stats = await this.apiCall('/admin/stats');
            this.updateStats(stats);

            // Load recent keys
            const recentKeys = await this.apiCall('/admin/recent-keys');
            this.updateRecentKeys(recentKeys);

            // Check system status
            await this.checkSystemStatus();

        } catch (error) {
            console.error('Error loading dashboard data:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    updateStats(stats) {
        document.getElementById('total-keys').textContent = stats.totalKeys || '0';
        document.getElementById('active-users').textContent = stats.activeUsers || '0';
        document.getElementById('success-rate').textContent = (stats.successRate || 0) + '%';
        document.getElementById('avg-time').textContent = (stats.avgTime || 0) + 's';
    }

    updateRecentKeys(keys) {
        const container = document.getElementById('recent-keys');
        
        if (!keys || keys.length === 0) {
            container.innerHTML = '<div class="loading">No recent keys found</div>';
            return;
        }

        container.innerHTML = keys.map(key => `
            <div class="key-item">
                <div class="key-info">
                    <div class="key-code">${key.key}</div>
                    <div class="key-time">${this.formatTime(key.created_at)}</div>
                </div>
                <div class="key-status ${key.status}">${key.status}</div>
            </div>
        `).join('');
    }

    async checkSystemStatus() {
        try {
            // Check database
            const dbStatus = await this.apiCall('/admin/status/database');
            document.getElementById('db-status').textContent = dbStatus.online ? 'Online' : 'Offline';
            document.getElementById('db-status').className = `status-indicator ${dbStatus.online ? 'online' : 'offline'}`;

            // Check API
            const apiStatus = await this.apiCall('/admin/status/api');
            document.getElementById('api-status').textContent = apiStatus.online ? 'Online' : 'Offline';
            document.getElementById('api-status').className = `status-indicator ${apiStatus.online ? 'online' : 'offline'}`;

            // Check key generation
            const keygenStatus = await this.apiCall('/admin/status/keygen');
            document.getElementById('keygen-status').textContent = keygenStatus.enabled ? 'Enabled' : 'Disabled';
            document.getElementById('keygen-status').className = `status-indicator ${keygenStatus.enabled ? 'online' : 'offline'}`;

        } catch (error) {
            console.error('Error checking system status:', error);
        }
    }

    async loadKeys() {
        try {
            const keys = await this.apiCall('/admin/keys');
            this.updateKeysTable(keys);
        } catch (error) {
            console.error('Error loading keys:', error);
            this.showError('Failed to load keys');
        }
    }

    updateKeysTable(keys) {
        const tbody = document.getElementById('keys-tbody');
        
        if (!keys || keys.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="loading">No keys found</td></tr>';
            return;
        }

        tbody.innerHTML = keys.map(key => `
            <tr>
                <td><code>${key.key}</code></td>
                <td><code>${key.hwid || 'Not bound'}</code></td>
                <td>${this.formatTime(key.created_at)}</td>
                <td>${this.formatTime(key.expires_at)}</td>
                <td><span class="status-indicator ${this.getKeyStatus(key)}">${this.getKeyStatus(key)}</span></td>
                <td>
                    <button class="btn btn-danger btn-sm" onclick="admin.revokeKey('${key.key}')">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="admin.extendKey('${key.key}')">
                        <i class="fas fa-clock"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async loadUsers() {
        try {
            const users = await this.apiCall('/admin/users');
            this.updateUsersTable(users);
        } catch (error) {
            console.error('Error loading users:', error);
            this.showError('Failed to load users');
        }
    }

    updateUsersTable(users) {
        const tbody = document.getElementById('users-tbody');
        
        if (!users || users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="loading">No users found</td></tr>';
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td><code>${user.hwid}</code></td>
                <td><code>${user.ip_hash}</code></td>
                <td>${user.key_count}</td>
                <td>${this.formatTime(user.last_active)}</td>
                <td><span class="status-indicator ${user.status}">${user.status}</span></td>
                <td>
                    <button class="btn btn-danger btn-sm" onclick="admin.banUser('${user.hwid}')">
                        <i class="fas fa-ban"></i>
                    </button>
                    <button class="btn btn-secondary btn-sm" onclick="admin.resetHwid('${user.hwid}')">
                        <i class="fas fa-refresh"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    async loadLogs() {
        try {
            const logs = await this.apiCall('/admin/logs');
            this.updateLogs(logs);
        } catch (error) {
            console.error('Error loading logs:', error);
            this.showError('Failed to load logs');
        }
    }

    updateLogs(logs) {
        const container = document.getElementById('logs-container');
        
        if (!logs || logs.length === 0) {
            container.innerHTML = '<div class="loading">No logs found</div>';
            return;
        }

        container.innerHTML = logs.map(log => `
            <div class="log-entry ${log.level}">
                <strong>[${this.formatTime(log.timestamp)}]</strong> 
                <span class="log-level">${log.level.toUpperCase()}</span>: 
                ${log.message}
            </div>
        `).join('');

        // Auto-scroll to bottom
        container.scrollTop = container.scrollHeight;
    }

    async apiCall(endpoint, options = {}) {
        const response = await fetch(`/.netlify/functions/admin${endpoint}`, {
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
            },
            ...options
        });

        if (!response.ok) {
            throw new Error(`API call failed: ${response.statusText}`);
        }

        return await response.json();
    }

    // Utility functions
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString();
    }

    getKeyStatus(key) {
        const now = new Date();
        const expires = new Date(key.expires_at);
        
        if (expires < now) return 'offline'; // expired
        if (key.hwid) return 'online'; // active
        return 'online'; // unused but valid
    }

    showError(message) {
        // Simple error notification
        alert(message);
    }

    showSuccess(message) {
        // Simple success notification
        alert(message);
    }

    // Admin actions
    async revokeKey(key) {
        if (!confirm('Are you sure you want to revoke this key?')) return;
        
        try {
            await this.apiCall(`/keys/${key}`, { method: 'DELETE' });
            this.showSuccess('Key revoked successfully');
            this.loadKeys();
        } catch (error) {
            this.showError('Failed to revoke key');
        }
    }

    async extendKey(key) {
        const hours = prompt('Extend key by how many hours?', '24');
        if (!hours) return;
        
        try {
            await this.apiCall(`/keys/${key}/extend`, {
                method: 'POST',
                body: JSON.stringify({ hours: parseInt(hours) })
            });
            this.showSuccess('Key extended successfully');
            this.loadKeys();
        } catch (error) {
            this.showError('Failed to extend key');
        }
    }

    async banUser(hwid) {
        if (!confirm('Are you sure you want to ban this user?')) return;
        
        try {
            await this.apiCall(`/users/${hwid}/ban`, { method: 'POST' });
            this.showSuccess('User banned successfully');
            this.loadUsers();
        } catch (error) {
            this.showError('Failed to ban user');
        }
    }

    async resetHwid(hwid) {
        if (!confirm('Are you sure you want to reset this HWID?')) return;
        
        try {
            await this.apiCall(`/users/${hwid}/reset`, { method: 'POST' });
            this.showSuccess('HWID reset successfully');
            this.loadUsers();
        } catch (error) {
            this.showError('Failed to reset HWID');
        }
    }

    async saveSettings() {
        const settings = {
            enableKeygen: document.getElementById('enable-keygen').checked,
            keyDuration: parseInt(document.getElementById('key-duration').value),
            enableHwid: document.getElementById('enable-hwid').checked,
            enableRatelimit: document.getElementById('enable-ratelimit').checked
        };

        try {
            await this.apiCall('/settings', {
                method: 'POST',
                body: JSON.stringify(settings)
            });
            this.showSuccess('Settings saved successfully');
        } catch (error) {
            this.showError('Failed to save settings');
        }
    }

    logout() {
        localStorage.removeItem('admin_token');
        window.location.href = '/admin/login.html';
    }

    startAutoRefresh() {
        // Refresh current section every 30 seconds
        setInterval(() => {
            if (this.currentSection === 'dashboard') {
                this.loadDashboardData();
            }
        }, 30000);
    }

    // Search functions
    searchKeys(query) {
        const rows = document.querySelectorAll('#keys-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(query.toLowerCase()) ? '' : 'none';
        });
    }

    searchUsers(query) {
        const rows = document.querySelectorAll('#users-tbody tr');
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(query.toLowerCase()) ? '' : 'none';
        });
    }
}

// Initialize admin dashboard
const admin = new AdminDashboard();
