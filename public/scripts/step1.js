async function verifyAndRedirect() {
  if (typeof grecaptcha === 'undefined') {
    alert('reCAPTCHA not loaded. Please refresh the page and try again.');
    return;
  }

  const recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert('Please complete the captcha first!');
    return;
  }

  if (recaptchaResponse.length < 20) {
    alert('Invalid reCAPTCHA response. Please try completing the captcha again.');
    grecaptcha.reset();
    return;
  }

  try {
    const selectedService = localStorage.getItem('selected_service') || 'linkvertise';

    const response = await fetch('/.netlify/functions/app/generate-token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ recaptchaResponse })
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Server validation failed');
    }

    const { token } = await response.json();

    localStorage.setItem('progression_token', token);
    localStorage.setItem('link_visit_start', Date.now().toString());

    await fetch('/.netlify/functions/app/track-link-visit', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: token,
        service: selectedService
      })
    });

    const shortenerUrls = {
      'linkvertise': 'https://link-target.net/1028057/yZwSOXUkU9sF',
      'shrinkme': 'https://shrinkme.io/keyaccess'
    };

    const targetUrl = shortenerUrls[selectedService] || shortenerUrls.linkvertise;
    const trackingUrl = `${targetUrl}?ref=keysystem-step1&t=${Date.now()}`;


    window.location.href = trackingUrl;
  } catch (error) {
    alert("Verification failed: " + error.message);

  }
}

function onCaptchaExpired() {
}

function onCaptchaError() {
  alert('reCAPTCHA error. Please refresh the page and try again.');
}

window.onload = function() {
  const token = localStorage.getItem('progression_token');
  const step2Completed = localStorage.getItem('step2_completed');

  if (token && step2Completed === 'true') {
    window.location.href = 'finals.html';
    return;
  }

  if (token) {
    window.location.href = 'step2.html';
    return;
  }

};
