let mouseInteractions = 0;
let pageLoadTime = Date.now();

document.addEventListener('mousemove', () => mouseInteractions++);
document.addEventListener('click', () => mouseInteractions += 2);

async function verifyAndProceed() {

  if (typeof grecaptcha === 'undefined') {
    alert('reCAPTCHA not loaded. Please refresh the page and try again.');
    return;
  }

  const recaptchaResponse = grecaptcha.getResponse();

  if (!recaptchaResponse) {
    alert('Please complete the captcha first!');
    return;
  }

  if (recaptchaResponse.length < 20) {
    alert('Invalid reCAPTCHA response. Please try completing the captcha again.');
    grecaptcha.reset();
    return;
  }

  try {
    const token = localStorage.getItem('progression_token');
    const linkVisitStart = localStorage.getItem('link_visit_start');

    if (!token) {
      alert('Invalid session. Please start from the beginning.');
      window.location.href = '/';
      return;
    }

    const timeSpent = linkVisitStart ? Date.now() - parseInt(linkVisitStart) : 0;

    const verificationData = {
      service: localStorage.getItem('selected_service') || 'linkvertise',
      timeSpent: timeSpent,
      mouseInteractions: mouseInteractions,
      screenWidth: screen.width,
      screenHeight: screen.height,
      pageLoadTime: pageLoadTime,
      referrer: document.referrer
    };



    const stepResponse = await fetch('/.netlify/functions/app/verify-step', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: token,
        step: 2,
        recaptchaResponse: recaptchaResponse,
        verificationData: verificationData
      })
    });

    if (!stepResponse.ok) {
      const errorData = await stepResponse.json();

      if (errorData.bypassDetected) {
        alert("⚠️ BYPASS DETECTED!\n\n" + errorData.error + "\n\nPlease use the official link shortener to support the project.");
        localStorage.clear();
        window.location.href = '/';
        return;
      }

      throw new Error(errorData.error || 'Step verification failed');
    }

    localStorage.setItem('step2_completed', 'true');
    localStorage.setItem('link_visit_start_step2', Date.now().toString());

    const secondLinkvertiseUrl = 'https://link-target.net/1028057/s8dgeigX8PDj';
    const trackingUrl = `${secondLinkvertiseUrl}?ref=keysystem-step2&t=${Date.now()}`;


    window.location.href = trackingUrl;
  } catch (error) {
    alert("Verification failed: " + error.message);

  }
}

function checkReferrerAndBypass() {
  const referrer = document.referrer.toLowerCase();

  const bypassIndicators = [
    'bypasscity.com',
    'bypass.city',
    'linkvertise.net',
    'shrink-me.io',
    'direct-link',
    'bypass',
    'skip',
    'free',
    'unlock'
  ];

  const isBypass = bypassIndicators.some(indicator => referrer.includes(indicator));

  if (isBypass) {
    alert('⚠️ BYPASS DETECTED!\n\nBypass services are not allowed. Please use the official link shortener to support the project.');
    localStorage.clear();
    window.location.href = '/';
    return false;
  }

  if (!referrer && !localStorage.getItem('progression_token')) {
    alert('Please start from the beginning.');
    window.location.href = '/';
    return false;
  }

  return true;
}

function onCaptchaExpired() {
}

function onCaptchaError() {
  alert('reCAPTCHA error. Please refresh the page and try again.');
}

window.addEventListener('load', function() {
  if (!checkReferrerAndBypass()) {
    return;
  }

  const token = localStorage.getItem('progression_token');
  if (!token) {
    alert('No valid session found. Please start from the beginning.');
    window.location.href = '/';
    return;
  }


});
