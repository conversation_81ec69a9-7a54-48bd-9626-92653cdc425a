const JavaScriptObfuscator = require('javascript-obfuscator');
const fs = require('fs');
const path = require('path');

// Configuration for obfuscation
const obfuscationOptions = {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.75,
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  debugProtection: false,
  debugProtectionInterval: 0,
  disableConsoleOutput: true,
  identifierNamesGenerator: 'hexadecimal',
  log: false,
  numbersToExpressions: true,
  renameGlobals: false,
  selfDefending: true,
  simplify: true,
  splitStrings: true,
  splitStringsChunkLength: 10,
  stringArray: true,
  stringArrayCallsTransform: true,
  stringArrayEncoding: ['base64'],
  stringArrayIndexShift: true,
  stringArrayRotate: true,
  stringArrayShuffle: true,
  stringArrayWrappersCount: 2,
  stringArrayWrappersChainedCalls: true,
  stringArrayWrappersParametersMaxCount: 4,
  stringArrayWrappersType: 'function',
  stringArrayThreshold: 0.75,
  transformObjectKeys: true,
  unicodeEscapeSequence: false
};

// Function to obfuscate a single file
function obfuscateFile(filePath, outputPath) {
  try {
    const sourceCode = fs.readFileSync(filePath, 'utf8');
    const obfuscationResult = JavaScriptObfuscator.obfuscate(sourceCode, obfuscationOptions);
    
    fs.writeFileSync(outputPath, obfuscationResult.getObfuscatedCode());
    console.log(`✅ Obfuscated: ${filePath} -> ${outputPath}`);
  } catch (error) {
    console.error(`❌ Error obfuscating ${filePath}:`, error.message);
  }
}

// Function to obfuscate all JS files in a folder
function obfuscateFolder(folderPath, outputFolder) {
  if (!fs.existsSync(folderPath)) {
    console.error(`❌ Folder not found: ${folderPath}`);
    return;
  }

  // Create output folder if it doesn't exist
  if (!fs.existsSync(outputFolder)) {
    fs.mkdirSync(outputFolder, { recursive: true });
  }

  // Read all files in the folder
  const files = fs.readdirSync(folderPath);
  
  files.forEach(file => {
    const filePath = path.join(folderPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Recursively obfuscate subdirectories
      const subOutputFolder = path.join(outputFolder, file);
      obfuscateFolder(filePath, subOutputFolder);
    } else if (file.endsWith('.js')) {
      // Obfuscate JS files
      const outputPath = path.join(outputFolder, file);
      obfuscateFile(filePath, outputPath);
    } else {
      // Copy non-JS files as-is
      const outputPath = path.join(outputFolder, file);
      fs.copyFileSync(filePath, outputPath);
      console.log(`📄 Copied: ${filePath} -> ${outputPath}`);
    }
  });
}

// Main function
function main() {
  const args = process.argv.slice(2);
  const target = args[0] || 'public';

  console.log(`🔒 Starting obfuscation for: ${target}`);
  console.log('⚙️  Using high-security obfuscation settings...\n');

  switch (target.toLowerCase()) {
    case 'public':
      obfuscateFolder('public/scripts', 'public/scripts-obfuscated');
      console.log('\n✅ Public scripts obfuscated!');
      console.log('📁 Output: public/scripts-obfuscated/');
      break;
      
    case 'netlify':
      obfuscateFolder('netlify/functions', 'netlify/functions-obfuscated');
      console.log('\n✅ Netlify functions obfuscated!');
      console.log('📁 Output: netlify/functions-obfuscated/');
      break;
      
    case 'all':
      obfuscateFolder('public/scripts', 'public/scripts-obfuscated');
      obfuscateFolder('netlify/functions', 'netlify/functions-obfuscated');
      console.log('\n✅ All folders obfuscated!');
      console.log('📁 Outputs: public/scripts-obfuscated/ & netlify/functions-obfuscated/');
      break;
      
    default:
      // Obfuscate custom folder
      const outputFolder = `${target}-obfuscated`;
      obfuscateFolder(target, outputFolder);
      console.log(`\n✅ ${target} obfuscated!`);
      console.log(`📁 Output: ${outputFolder}/`);
      break;
  }

  console.log('\n🔒 Obfuscation complete!');
  console.log('⚠️  Remember to test the obfuscated code before deploying!');
}

// Run the script
main();
