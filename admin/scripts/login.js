// Admin Login JavaScript
class AdminLogin {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkExistingAuth();
    }

    setupEventListeners() {
        const loginForm = document.getElementById('loginForm');
        loginForm.addEventListener('submit', (e) => this.handleLogin(e));

        // Enter key support
        document.getElementById('adminCode').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleLogin(e);
            }
        });
    }

    async handleLogin(e) {
        e.preventDefault();
        
        const adminCode = document.getElementById('adminCode').value.trim();
        const loginBtn = document.getElementById('loginBtn');
        
        if (!adminCode) {
            this.showMessage('Please enter an admin code', 'error');
            return;
        }

        // Show loading state
        loginBtn.classList.add('loading');
        loginBtn.disabled = true;

        try {
            const isValid = await this.validateAdminCode(adminCode);
            
            if (isValid) {
                // Store auth token
                localStorage.setItem('admin_token', this.generateToken(adminCode));
                localStorage.setItem('admin_auth_time', Date.now().toString());
                
                this.showMessage('Access granted! Redirecting...', 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = '/admin/index.html';
                }, 1500);
            } else {
                this.showMessage('Invalid admin code. Access denied.', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showMessage('Login failed. Please try again.', 'error');
        } finally {
            // Remove loading state
            loginBtn.classList.remove('loading');
            loginBtn.disabled = false;
        }
    }

    async validateAdminCode(code) {
        // Simple hardcoded admin codes - you can change these
        const validCodes = [
            'MADARA2024',
            'ProjectMadara',
        ];

        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check if code is valid
        return validCodes.includes(code.toUpperCase());
    }

    generateToken(code) {
        // Simple token generation (in production, use proper JWT)
        const timestamp = Date.now();
        const hash = btoa(`${code}:${timestamp}:admin`);
        return hash;
    }

    showMessage(text, type) {
        // Remove existing messages
        const existingMessage = document.querySelector('.message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create new message
        const message = document.createElement('div');
        message.className = `message ${type}`;
        
        const icon = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
        message.innerHTML = `
            <i class="${icon}"></i>
            <span>${text}</span>
        `;

        // Insert before form
        const form = document.getElementById('loginForm');
        form.parentNode.insertBefore(message, form);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 5000);
    }

    checkExistingAuth() {
        const token = localStorage.getItem('admin_token');
        const authTime = localStorage.getItem('admin_auth_time');
        
        if (token && authTime) {
            const now = Date.now();
            const authAge = now - parseInt(authTime);
            const maxAge = 24 * 60 * 60 * 1000; // 24 hours
            
            if (authAge < maxAge) {
                // Still valid, redirect to dashboard
                window.location.href = '/admin/index.html';
                return;
            } else {
                // Expired, clear storage
                localStorage.removeItem('admin_token');
                localStorage.removeItem('admin_auth_time');
            }
        }
    }
}

// Initialize login system
document.addEventListener('DOMContentLoaded', () => {
    new AdminLogin();
});

// Add some visual effects
document.addEventListener('DOMContentLoaded', () => {
    // Animate floating icons
    const icons = document.querySelectorAll('.floating-icon');
    icons.forEach((icon, index) => {
        icon.style.animationDelay = `${index * 0.5}s`;
    });

    // Focus on input field
    document.getElementById('adminCode').focus();
});
